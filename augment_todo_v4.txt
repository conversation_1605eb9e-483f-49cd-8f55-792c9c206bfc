[v4 清理与跟进清单]

- 并发改造（已完成）
  - outline_refine_task（@task 并发 + 节点内聚合）
  - scene_design_task（@task 并发 + 节点内聚合）
  - stream_post_task（@task 并发 + 节点内聚合）
  - .result() → await 收集双保险，避免同 tick InvalidStateError
  - 移除 join/spawn 旧实现与状态字段

- latest 恢复（已完成）
  - apps/main.py：latest 仅选择 thread_id/book_id，不固定 checkpoint_id，以便继续写作
  - latest 恢复时，动态提升 write_chapters_cnt = 当前已写章节数 + 基础 N（至少 +1）

- 遗留清理（进行中）
  - 删除 stream_write_tasks.py 中旧的 writer_stream_post_* 兼容函数（已完成）
  - 全库检索未使用导出/导入、废弃注释、死代码（下一步）
  - 校验 __all__ 导出列表与实际使用匹配（下一步）

- 测试建议
  - 功能：chapters=1/2/5/10 多轮长跑，观察并发稳定性与 token 成本
  - 单测：mock llm_json / store_manager，覆盖三个 @task 的入参/出参及落盘幂等
  - 恢复：--checkpoint-id latest 能继续写作（不 main end）

- 风险与监控
  - 若最新 checkpoint 的 state 缺失 book_detail，latest 将回落到交互/新建
  - 场景/大纲向量存储失败时静默，需在日志中监控

- 下一步（待定）
  - 扩展场景并发范围（按卷窗口可配置）
  - stream_post_task 可增加轻量重试/降级策略

