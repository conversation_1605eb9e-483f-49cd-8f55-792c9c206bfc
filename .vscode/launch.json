{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "main",
            "type": "debugpy",
            "envFile": "${workspaceFolder}/.env",
            "request": "launch",
            "program": "${workspaceFolder}/apps/main.py",
            "console": "integratedTerminal",
            "justMyCode": false
        },
        {
            "name": "main-no-review",
            "type": "debugpy",
            "envFile": "${workspaceFolder}/.env",
            "request": "launch",
            "program": "${workspaceFolder}/apps/main.py",
            "console": "integratedTerminal",
            "args": ["--no-review"],
            "justMyCode": false
        },
        
        {
            "name": "zego_test",
            "type": "debugpy",
            "envFile": "${workspaceFolder}/.env",
            "request": "launch",
            "program": "${workspaceFolder}/apps/zego_test.py",
            "console": "integratedTerminal",
            "justMyCode": false
        },
        {
            "name": "resume",
            "type": "debugpy",
            "envFile": "${workspaceFolder}/.env",
            "request": "launch",
            "program": "${workspaceFolder}/apps/resume.py",
            "console": "integratedTerminal",
            "justMyCode": false
        }, 
        {
            "name": "store_test",
            "type": "debugpy",
            "envFile": "${workspaceFolder}/.env",
            "request": "launch",
            "program": "${workspaceFolder}/src/memory/store_test.py",
            "console": "integratedTerminal",
            "justMyCode": false
        },    
        {
            "name": "test_cot_llm",
            "type": "debugpy",
            "envFile": "${workspaceFolder}/.env",
            "request": "launch",
            "program": "${workspaceFolder}/tests/test_cot_llm.py",
            "console": "integratedTerminal",
            "justMyCode": false
        },        
        {
            "name": "writer_store_test",
            "type": "debugpy",
            "envFile": "${workspaceFolder}/.env",
            "request": "launch",
            "program": "${workspaceFolder}/src/teams/writer_team/store/test_writer_store.py",
            "console": "integratedTerminal",
            "justMyCode": false
        },      {
            "name": "vector_query",
            "type": "debugpy",
            "envFile": "${workspaceFolder}/.env",
            "request": "launch",
            "program": "${workspaceFolder}/apps/vector_query.py",
            "console": "integratedTerminal",
            "justMyCode": false
        },   
        {
            "name": "test_progress_simple",
            "type": "debugpy",
            "envFile": "${workspaceFolder}/.env",
            "request": "launch",
            "program": "${workspaceFolder}/src/teams/writer_team/test_progress_simple.py",
            "console": "integratedTerminal",
            "justMyCode": false
        },        {
            "name": "test_tool_node",
            "type": "debugpy",
            "envFile": "${workspaceFolder}/.env",
            "request": "launch",
            "program": "${workspaceFolder}/tests/test_tool_node.py",
            "console": "integratedTerminal",
            "justMyCode": false
        },        
        // ,{
        //     "name": "langgraph",
        //     "type": "debugpy",
        //     "envFile": "${workspaceFolder}/.env",
        //     "console": "integratedTerminal",
        //     "request": "launch",
        //     "module": "langgraph",
        //     "args": ["dev"]
        // }
    ]
}