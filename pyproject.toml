[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "QFlowAgent"
version = "0.1.0"
description = "QFlowAgent"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "langgraph>=0.5.4",
    "langchain>=0.3.27",
    "dotenv",
    "langchain-openai>=0.3.28",
    "httpx[socks]>=0.27.0",
    "json-repair>=0.48.0",
    "langchain-community>=0.3.27",
    "psycopg[binary,pool]>=3.2.9",
    "psycopg-pool>=3.2.6",
    "langgraph-checkpoint-postgres>=2.0.21",
    "PySocks>=1.7.1",
    "langchain-postgres>=0.0.14",
    "dashscope>=1.23.3",
    "greenlet>=3.2.2",
    "langchain-deepseek>=0.1.3",
    "openai>=1.99.6",
    "ruff>=0.12.8",
]

[project.optional-dependencies]
test = [
    "pytest",
    "pytest-cov",
    "pytest-asyncio",
    "vcrpy",
]
dev = [
    "langgraph-cli[inmem]" 
]

[tool.setuptools.packages.find]
where = ["."]
include = ["src*"]

[tool.black]
line-length = 120

[tool.ruff]
exclude = [
  "tests",
  "unitest",
  "src/tools",
  "src/memory/store_test.py",
]
line-length = 120

[dependency-groups]
dev = [
    "ruff>=0.12.8",
]
