# QFlowAgent 创作流程（增量场景/增量分卷版）

本说明阐述“边写边规划”的目标：
- 场景与分卷不再一次性生成全书，而是按需增量生成
- 写作流在耗尽“当前卷的可用场景”时触发补充；卷写完则触发下一卷规划
- 写作过程中持续维护多层次摘要（L1/L2/L3），为模型提供更强的历史上下文

---

## 总体流程图

```mermaid
flowchart TD
  subgraph 规划与设定
    P1[bs_planner: 设定策划] --> P2[bs_steps: 执行设定步骤]
    P2 --> P3[bs_refine: 设定精炼]
    P3 --> O1[writer_outline: 卷纲初始化]
    O1 --> S1[writer_scene: 场景初始化/增量补充]
  end

  subgraph 写作主线
    W1[stream_write: 流式写作(只产正文)] -->|缓冲达阈值| W2[chapter_segment: 智能分章]
    W2 --> W3[prepare_next_chapter: L1/L2/L3 摘要 + 场景/卷判定]
    W3 -->|need_switch_scene=True| W1
    W3 -->|need_switch_scene=False| W1
  end

  %% 场景/卷增量触发
  W3 -- 场景耗尽 --> S1
  S1 -- 当前卷补足完成 --> W1

  %% 分卷增量触发
  S1 -- 当前卷预计章节完成 --> O2[writer_outline: 下一卷增量大纲]
  O2 --> S1
```

---

## 核心节点职责（简述）

- writer_outline
  - 初始化：生成起始卷纲（可一卷或若干卷）
  - 增量：当当前卷预计章节写满，触发“下一卷”的大纲生成（只生成下一卷，不一口气全书）

- writer_scene
  - 初始化：基于当前卷纲生成该卷完整或部分场景清单（可分批）
  - 增量：
    - 当写作推进、场景耗尽：补充“当前卷剩余场景”
    - 当卷完成：等待/接续“下一卷卷纲”，为下一卷生成场景

- stream_write
  - 只产正文，按 writing_state 调整提示（IN_SCENE 深入推进；SWITCHING_SCENE 过渡衔接）
  - 注入最近 L1/L2/L3 摘要和必要场景提示

- chapter_segment
  - 从缓冲中切出一章（title/正文/剩余），剩余回灌缓冲

- prepare_next_chapter
  - 生成 L1（每章）、L2（每10章）、L3（每50章）摘要，写入 state.book_summary
  - 评估是否切换场景；若耗尽则 goto writer_scene
  - 评估“当前卷预计是否完成”并推动下一卷大纲增量（由 writer_scene 内部或其后续触发）

---

## 状态与数据

- State
  - writing_state: IN_SCENE | SWITCHING_SCENE
  - book_detail: 包含 volume_outlines, chapters, stream_buffer
  - scene_design: 各卷场景清单（支持增量）
  - book_summary: BookSumary（L1/L2/L3）

- BookSumary
  - L1：summary_l1_by_chapter[chapter_number] = str
  - L2：summary_l2_by_block[block10] = str
  - L3：summary_l3_by_block[block50] = str

---

## 改造目标与范围

1) 场景增量化
- 当 prepare_next_chapter 判定“场景耗尽”，writer_scene 仅生成“当前卷所需下一批场景”，不必一次性补满整卷
- 根据写作进度（章节号）估算还需多少场景（例如最近 10~20 章）

2) 分卷增量化
- 当“当前卷预计章节数已达”，writer_scene 触发对下一卷的场景需求；若无下一卷纲，则先调用 writer_outline 生成下一卷大纲
- writer_outline 改为“只生成下一卷”，并与已有大纲合并

3) 上下文增强
- stream_write/prepare_next_chapter 注入 L1/L2/L3 摘要与下一场景提示

---

## 实施计划（高层）

- Phase A（已完成）
  - 流写 schema 注入问题修复；分章三字段；prepare_next_chapter 摘要+判定；写作状态化；注入摘要上下文

- Phase B（本次）
  - writer_scene：增加“按需补充场景”路径（根据章节进度/estimated_chapters 判断缺口），若卷完成则准备下一卷
  - writer_outline：提供“生成下一卷”的接口，合并到 book_detail.volume_outlines
  - 图编排：在 prepare_next_chapter -> writer_scene 处，让 writer_scene 能够根据“卷完成/场景缺口”决定是否调用 writer_outline 以增量生成下一卷，再继续场景增量

- Phase C（测试与完善）
  - 单测覆盖：场景缺口补齐、卷完成触发下一卷、写回与回路

---

## 断言（约束）
- 不保留兼容层，直接迁移调用方；新增字段/流转在现有节点最小改动落地
- 每一步都确保可控（按需生成），避免“全书一次性生成”导致的质量与上下文偏差
- 适当添加 assert，尽早暴露问题

