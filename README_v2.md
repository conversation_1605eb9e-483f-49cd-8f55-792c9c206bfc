## QFlowAgent v2 概览

---

### v2 特性
- 函数式节点 + LangGraph 原生：移除厚重基类，节点以 `async def node(state, config) -> Command` 统一返回更新与跳转。
- 审核：已移除。流程统一自动推进。
- 任务化并发：大纲/角色/场景/流式后处理与章节入库采用 `spawn/join`，支持限流、requeue、队列观测与可重入。
- 统一 Streaming 与可观测性：Runner 使用 `astream_events(...)`，CLI 统一渲染 `on_*` 事件。
- 数据层 v2-only：命名空间统一为 `("writer_app_v2", book_id, ...)` 且强制 `schema_version=2`，不再保留 v1 兼容代码。

---

### 关键特性
- 主图与节点
  - 入口：统一为 `bs_planner`（恢复与回退由应用层与 checkpointer 直接处理，不再经 `progress_check` 节点）
  - 设定：`bs_planner_fn` → `bs_steps_fn` → `bs_refine_fn`
  - 角色：`writer_character_fn`（并发生成详情，join 汇总）
  - 大纲：`writer_outline_fn`（初始化失败→回退重试→占位草稿；并发细化 join）
  - 场景：`writer_scene_fn`（并发设计 join）
  - 写作：`stream_write_fn`（触发流式后处理 spawn/join → `chapter_segment_fn`）
  - 章节入库：`writer_chapter_store_node`（单节点简化实现）

- 并发与限流（队列观测一致）
  - 流式后处理：已合并回 `stream_write` 节点内部，使用 `stream_post_task` 并发处理分块；
    - 读取 `stream_post_*` 限流配置；
    - 生成 `chapters_stream/batch-<id>/批量-流式后处理.md`。
  - 章节入库：`writer_chapter_store_node`
    - 输入期望章节集合或自动从 `book_detail.chapters` 推断；
    - 产出 `chapter_store_result_<n>` 标记；
    - 写入 `resume_info` 摘要。
  - 流式后处理分块：由 `config.configurable.stream_post_chunk_size` 控制（默认 2000）。

- CLI 事件：打印事件到终端或用户自定义重定向（无人工审核交互）。

- 数据与命名空间
  - 统一写入 `schema_version=2`；命名空间 `("writer_app_v2", book_id, data_type)`；
  - `WriterStoreManager` 提供 `plan/settings/outline/scene/chapters/stream` 的存取与分块向量化；
  - 幂等键规范：章节全文 `chapter_{N}_{title}_full`，分块 `chapter_{N}_{title}_chunk_{i}`；流式分块 `batch_{batch_id}_chunk_{i:03d}`。

- 稳健性
  - 结构化：`NodeKit.llm_json` 内置 Schema 回显检测与一次回退重试；
  - 文本：`NodeKit.llm_text` 支持最小长度/敏感模式校验，一次回退重试与可选 tier 升级。

---

### 功能清单
- requeue 与降级占位：join 节点对超时在飞任务进行重派发；超过阈值写入占位结果 `error: "requeue_exceeded"`，确保 join 可收敛。
 交互运行：`uv run apps/main.py`（默认递归限制 100）。可选通过 `--book-name` 指定书名，不传则由策划阶段自动确定。
- 恢复：支持 `--checkpoint-id latest` 从最新 checkpoint 恢复；也可指定 `checkpoint_id` 回退。

---

### 已移除
- v1 数据层与示例：移除 `writer_app` 命名空间与 `schema_version=1` 的任何读写/脚本/文档。
- 历史类节点与兼容层：移除 `HumanReviewNode`、`FlowControlNode`、`StructuredDataNode`、`MultiStageNode`、`IterativeProcessingNode`；不再保留 `maybe_set_human_review` 与自定义流打印回调。
- 统一 API：已移除 `get_response_from_astream(..., callback)` 的 callback 形参及相关调用点。

---

### 已验证
- 单测：`unitest/test_join_nodes.py` 覆盖 join 在飞预览长度、requeue 超阈占位、边界输入稳健性等；全部通过。
- 端到端：`uv run apps/main.py`（无人工审核）。
- 工具链：交互式恢复/删除集成于 `uv run apps/main.py`。

---

### 维护建议
- 轻微遗留（已完成）：已将 `kStateRestoreNamespace_commit_state/head` 常量抽取到 `src/checkpoint/constants.py`，并更新了所有引用；已删除历史 `progress_manager.py` 文件。
- 清理：删除空/废弃目录 `src/nodes/human_review/`、`src/nodes/chatper/`（拼写残留）、`src/nodes/store/`、`src/context_manager/`、`src/config/`，不再保留兼容路径。
- 文档对齐：若后续新增配置项（如更多限流阈值/观测项），同步更新 `README_nodes.md` 与本文件。

---

### 运行与使用
- 统一启动：`uv run apps/main.py`
- 语义检索：`uv run apps/vector_query.py --book-id <book_id>`
- 单测：`uv run pytest -q unitest`

---

### 参考
- 入口：`src/functional/entrypoint.py`
- 工具层：`src/nodes/common/node_kit.py`
- 任务与并发：`src/nodes/**/{outline,scene,character,chapter}*`
- 存储：`src/nodes/common/store_manager.py`
- Checkpoint：`src/checkpoint/checkpoint_manager.py`、`README_checkpoint.md`
