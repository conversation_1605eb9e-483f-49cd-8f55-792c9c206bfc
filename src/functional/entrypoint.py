from __future__ import annotations

from typing import Any, Dict, Optional

from langgraph.config import get_config
from langgraph.func import entrypoint
from src.nodes.book_setting.bs_planner_functional import bs_planner_fn
from src.nodes.book_setting.bs_refine_functional import bs_refine_fn
from src.nodes.book_setting.bs_steps_functional import bs_steps_fn
from src.nodes.chapter.chapter_segment_functional import chapter_segment_fn
from src.nodes.chapter.prepare_next_chapter_functional import prepare_next_chapter_fn
from src.nodes.chapter.stream_write_functional import stream_write_fn
from src.nodes.character.character_functional import writer_character_fn
from src.nodes.outlines.outline_functional import writer_outline_fn
from src.nodes.scene.scene_functional import writer_scene_fn
from src.state import State


def _merge_update(state: Dict[str, Any], update: Optional[Dict[str, Any]]) -> None:
    if not update:
        return
    for k, v in update.items():
        if k == "resume_info":
            exist = state.get("resume_info")
            if isinstance(exist, list) and isinstance(v, list):
                exist.extend(v)
            else:
                state["resume_info"] = list(v) if isinstance(v, list) else [v]
        else:
            state[k] = v


def _last_next_from_resume(state: Dict[str, Any], default: str = "bs_planner") -> str:
    try:
        resume = state.get("resume_info") or []
        if isinstance(resume, list) and resume:
            last = resume[-1]
            if isinstance(last, dict):
                nxt = last.get("next") if last.get("next") is not None else last.get("next_node")
                if isinstance(nxt, str) and nxt.strip():
                    return nxt
    except Exception:
        pass
    return default


async def _run_one(node: str, state: Dict[str, Any]) -> tuple[Dict[str, Any], Optional[str]]:
    cfg = get_config()
    if node == "bs_planner":
        cmd = await bs_planner_fn(state, cfg)
    elif node == "bs_steps":
        # Functional 模式下无 runtime，上游实现接受可选 runtime
        cmd = await bs_steps_fn(state, cfg, None)
    elif node == "bs_refine":
        cmd = await bs_refine_fn(state, cfg)
    elif node == "writer_character":
        cmd = await writer_character_fn(state, cfg)
    elif node == "writer_outline":
        cmd = await writer_outline_fn(state, cfg)
    elif node == "writer_scene":
        cmd = await writer_scene_fn(state, cfg)  # type: ignore[name-defined]
    elif node == "stream_write":
        cmd = await stream_write_fn(state, cfg)
    elif node == "chapter_segment":
        cmd = await chapter_segment_fn(state, cfg)
    elif node == "prepare_next_chapter":
        cmd = await prepare_next_chapter_fn(state, cfg)
    elif node == "__end__":
        return {}, "__end__"
    else:
        # 未知节点：直接结束
        return {}, "__end__"

    update = getattr(cmd, "update", {})
    goto = getattr(cmd, "goto", None)
    return update or {}, goto


async def qflow_workflow_impl(inputs: dict, *, previous: Optional[State]) -> entrypoint.final[dict, State]:
    """
    QFlow 主流程核心实现（无装饰器）。
    由 build_qflow_workflow(checkpointer) 在运行期包裹为 Functional Entrypoint。
    """
    # 初始化/恢复 state
    state: Dict[str, Any] = dict(previous or {})
    if not state:
        msgs = None
        try:
            msgs = inputs.get("messages")
            if msgs is None and isinstance(inputs.get("llm_input"), dict):
                msgs = inputs.get("llm_input", {}).get("messages")
        except Exception:
            msgs = None
        if msgs:
            state["messages"] = msgs
        state.setdefault("resume_info", [])

    cfg = get_config()
    # 递归/步数限制（兜底 100）
    try:
        max_steps = int(getattr(cfg, "get", lambda *_: None)("recursion_limit") or 100)
    except Exception:
        max_steps = 100

    next_node = inputs.get("start_at") or state.get("next_node") or _last_next_from_resume(state, "bs_planner")
    steps = 0
    while isinstance(next_node, str) and next_node not in ("__end__", "") and steps < max_steps:
        update, goto = await _run_one(next_node, state)
        _merge_update(state, update)
        next_node = goto or _last_next_from_resume(state, "__end__")
        state["next_node"] = next_node
        steps += 1

    # decouple：返回轻量结果，保存完整 state
    return entrypoint.final(value={"next": next_node, "steps": steps}, save=state)


def build_qflow_workflow(checkpointer) -> Any:
    """在运行期用给定 checkpointer 构建可流式的 Functional Entrypoint。"""
    return entrypoint(checkpointer=checkpointer)(qflow_workflow_impl)


# 为 Dev Server / SDK 提供一个可直接导入的 Entrypoint（与 langgraph.json 对齐）
# 注意：不在模块层绑定数据库连接，避免异步上下文问题；服务端可注入 checkpointer。
qflow_workflow = entrypoint()(qflow_workflow_impl)


__all__ = ["build_qflow_workflow", "qflow_workflow_impl", "qflow_workflow"]
