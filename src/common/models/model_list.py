import os
from typing import List

from dotenv import load_dotenv
from pydantic import BaseModel, Field


load_dotenv(os.path.join(os.path.dirname(__file__), ".env"))
assert os.getenv("DEEPSEEK_BASE_URL") is not None
assert os.getenv("PPIO_BASE_URL") is not None
assert os.getenv("ALI_BASE_URL") is not None
assert os.getenv("SHANGTANG_BASE_URL") is not None
assert os.getenv("OPENROUTER_BASE_URL") is not None
assert os.getenv("DEEPSEEK_API_KEY") is not None
assert os.getenv("PPIO_API_KEY") is not None
assert os.getenv("ALI_API_KEY") is not None
assert os.getenv("SHANGTANG_API_KEY") is not None
assert os.getenv("OPENROUTER_API_KEY") is not None
assert os.getenv("GLM_BASE_URL") is not None
assert os.getenv("GLM_API_KEY") is not None
assert os.getenv("KIMI_BASE_URL") is not None
assert os.getenv("KIMI_API_KEY") is not None


class Provider(BaseModel):
    provider_name: str
    base_url: str
    api_key: str


class Model(BaseModel):
    name: str
    code: str
    provider_name: str
    context_length: int
    input_price: float
    output_price: float
    max_input_tokens: int
    max_output_tokens: int
    max_cot_tokens: int = 0
    chain_of_thought: bool
    tool_calls: bool = False
    extra_body: dict = {}
    params: List[str] = Field(default_factory=list)
    tips: str = ""


# 提供商数据
providers_data = {
    "DEEPSEEK": Provider(
        provider_name="deepseek",
        base_url=os.getenv("DEEPSEEK_BASE_URL"),
        api_key=os.getenv("DEEPSEEK_API_KEY"),
    ),
    "PPIO": Provider(
        provider_name="ppio",
        base_url=os.getenv("PPIO_BASE_URL"),
        api_key=os.getenv("PPIO_API_KEY"),
    ),
    "ALI": Provider(
        provider_name="ali",
        base_url=os.getenv("ALI_BASE_URL"),
        api_key=os.getenv("ALI_API_KEY"),
    ),
    "SHANGTANG": Provider(
        provider_name="shangtang",
        base_url=os.getenv("SHANGTANG_BASE_URL"),
        api_key=os.getenv("SHANGTANG_API_KEY"),
    ),
    "OPENROUTER": Provider(
        provider_name="openrouter",
        base_url=os.getenv("OPENROUTER_BASE_URL"),
        api_key=os.getenv("OPENROUTER_API_KEY"),
    ),
    "GLM": Provider(
        provider_name="glm",
        base_url=os.getenv("GLM_BASE_URL"),
        api_key=os.getenv("GLM_API_KEY"),
    ),
    "KIMI": Provider(
        provider_name="kimi",
        base_url=os.getenv("KIMI_BASE_URL"),
        api_key=os.getenv("KIMI_API_KEY"),
    ),
}

# 模型数据
ali_models = [
    Model(
        name="qwen-plus-latest",
        code="qwen-plus-latest",
        provider_name="ali",
        context_length=128,
        input_price=0.8,
        output_price=2,
        max_input_tokens=128,
        max_output_tokens=32,
        chain_of_thought=False,
        tool_calls=True,
        params=[],
        tips="可开启 enable_thinking, 详见 qwen-plus-latest:thinking",
    ),
    Model(
        name="qwen-plus-latest:thinking",
        code="qwen-plus-latest",
        params=["enable_thinking"],
        provider_name="ali",
        context_length=128,
        input_price=0.8,
        output_price=16,
        max_input_tokens=128,
        max_output_tokens=32,
        max_cot_tokens=38,
        chain_of_thought=True,
        tool_calls=True,
        extra_body={"enable_thinking": True},
        tips='需要 extra_body={"enable_thinking": True}, 不开详见 qwen-plus-latest',
    ),
    Model(
        name="qwen-plus-2025-04-28",
        code="qwen-plus-2025-04-28",
        provider_name="ali",
        context_length=128,
        input_price=0.8,
        output_price=2,
        max_input_tokens=128,
        max_output_tokens=32,
        chain_of_thought=False,
        tool_calls=True,
        params=[],
        tips="可开启 enable_thinking, 详见 qwen-plus-2025-04-28:thinking",
    ),
    Model(
        name="qwen-plus-2025-04-28:thinking",
        code="qwen-plus-2025-04-28",
        params=["enable_thinking"],
        provider_name="ali",
        context_length=128,
        input_price=0.8,
        output_price=16,
        max_input_tokens=128,
        max_output_tokens=32,
        max_cot_tokens=38,
        chain_of_thought=True,
        tool_calls=True,
        extra_body={"enable_thinking": True},
        tips='需要 extra_body={"enable_thinking": True}, 不开详见 qwen-plus-2025-04-28',
    ),
    Model(
        name="qwen-flash",
        code="qwen-flash",
        provider_name="ali",
        context_length=1000,
        max_input_tokens=1000,
        max_output_tokens=16,
        max_cot_tokens=0,
        input_price=0.3,
        output_price=0.6,
        chain_of_thought=False,
        tool_calls=False,
        tips="可开启 enable_thinking, 详见 qwen-plus-turbo:thinking",
    ),
    Model(
        name="qwen-flash:thinking",
        code="qwen-flash",
        params=["enable_thinking"],
        provider_name="ali",
        context_length=128,
        max_input_tokens=126,
        max_output_tokens=16,
        max_cot_tokens=38,
        input_price=0.3,
        output_price=6,
        chain_of_thought=True,
        tool_calls=True,
        extra_body={"enable_thinking": True},
        tips='需要 extra_body={"enable_thinking": True}, 不开详见 qwen-plus-latest',
    ),
    Model(
        name="qwen3-235b-a22b-instruct-2507",
        code="qwen3-235b-a22b-instruct-2507",
        provider_name="ali",
        context_length=128,
        input_price=2.4,
        output_price=9.6,
        max_input_tokens=128,
        max_output_tokens=32,
        max_cot_tokens=0,
        chain_of_thought=False,
        tool_calls=False,
    ),
    Model(
        name="qwen3-235b-a22b-thinking-2507",
        code="qwen3-235b-a22b-thinking-2507",
        provider_name="ali",
        context_length=128,
        input_price=2.4,
        output_price=9.6,
        max_input_tokens=128,
        max_output_tokens=32,
        max_cot_tokens=0,
        chain_of_thought=True,
        tool_calls=False,
    ),
    Model(
        name="qwen3-30b-a3b-instruct-2507",
        code="qwen3-30b-a3b-instruct-2507",
        provider_name="ali",
        context_length=128,
        input_price=2.4,
        output_price=9.6,
        max_input_tokens=128,
        max_output_tokens=32,
        max_cot_tokens=0,
        chain_of_thought=False,
        tool_calls=False,
    ),
    Model(
        name="qwen3-30b-a3b-thinking-2507",
        code="qwen3-30b-a3b-thinking-2507",
        provider_name="ali",
        context_length=128,
        input_price=2.4,
        output_price=9.6,
        max_input_tokens=128,
        max_output_tokens=32,
        max_cot_tokens=0,
        chain_of_thought=True,
        tool_calls=False,
    ),
    Model(
        name="qwen3-235b-a22b-instruct-2507-ppio",
        code="qwen/qwen3-235b-a22b-instruct-2507",
        provider_name="ppio",
        context_length=128,
        input_price=2.4,
        output_price=9.6,
        max_input_tokens=128,
        max_output_tokens=32,
        max_cot_tokens=0,
        chain_of_thought=False,
        tool_calls=False,
    ),
    Model(
        name="qwen3-235b-a22b-thinking-2507-ppio",
        code="qwen/qwen3-235b-a22b-thinking-2507",
        provider_name="ppio",
        context_length=128,
        input_price=2.4,
        output_price=9.6,
        max_input_tokens=128,
        max_output_tokens=32,
        max_cot_tokens=0,
        chain_of_thought=True,
        tool_calls=False,
    ),
]

deepseek_models = [
    Model(
        name="deepseek-reasoner",
        code="deepseek-reasoner",
        provider_name="deepseek",
        context_length=64,
        input_price=4,
        output_price=16,
        max_input_tokens=128,
        max_output_tokens=32,
        max_cot_tokens=0,
        chain_of_thought=True,
        tool_calls=False,
    ),
    Model(
        name="deepseek-chat",
        code="deepseek-chat",
        provider_name="deepseek",
        context_length=64,
        input_price=2,
        output_price=8,
        max_input_tokens=128,
        max_output_tokens=32,
        max_cot_tokens=0,
        chain_of_thought=False,
        tool_calls=False,
    ),
    Model(
        name="deepseek-chat-ali",
        code="deepseek-v3",
        provider_name="ali",
        context_length=64,
        input_price=2,
        output_price=8,
        max_input_tokens=128,
        max_output_tokens=32,
        max_cot_tokens=0,
        chain_of_thought=True,
        tool_calls=False,
    ),
    Model(
        name="deepseek-reasoner-ppio",
        code="deepseek/deepseek-r1-0528",
        provider_name="ppio",
        context_length=64,
        input_price=4,
        output_price=16,
        max_input_tokens=128,
        max_output_tokens=32,
        max_cot_tokens=0,
        chain_of_thought=True,
        tool_calls=False,
    ),
]


kimi_models = [
    Model(
        name="kimi-k2-ppio",
        code="moonshotai/kimi-k2-instruct",
        provider_name="ppio",
        context_length=64,
        input_price=4,
        output_price=16,
        max_input_tokens=128,
        max_output_tokens=32,
        max_cot_tokens=0,
        chain_of_thought=True,
        tool_calls=False,
    ),
    Model(
        name="kimi-k2-turbo-preview",
        code="kimi-k2-turbo-preview",
        provider_name="kimi",
        context_length=64,
        input_price=4,
        output_price=16,
        max_input_tokens=128,
        max_output_tokens=32,
        max_cot_tokens=0,
        chain_of_thought=True,
        tool_calls=False,
    ),
]

glm_models = [
    Model(
        name="glm-4.5-air",
        code="glm-4.5-air",
        provider_name="glm",
        context_length=64,
        input_price=4,
        output_price=16,
        max_input_tokens=128,
        max_output_tokens=32,
        max_cot_tokens=0,
        chain_of_thought=True,
        tool_calls=False,
    ),
    Model(
        name="glm-4.5",
        code="glm-4.5",
        provider_name="glm",
        context_length=64,
        input_price=4,
        output_price=16,
        max_input_tokens=128,
        max_output_tokens=32,
        max_cot_tokens=0,
        chain_of_thought=True,
        tool_calls=False,
    ),
    Model(
        name="glm-4.5-ppio",
        code="zai-org/glm-4.5",
        provider_name="ppio",
        context_length=64,
        input_price=4,
        output_price=16,
        max_input_tokens=128,
        max_output_tokens=32,
        max_cot_tokens=0,
        chain_of_thought=True,
        tool_calls=False,
    ),
]

models_data = [*ali_models, *deepseek_models, *kimi_models, *glm_models]


def get_llm_params(model_name):
    try:
        llm = next((model for model in models_data if model.name.startswith(model_name)), None)
        provider = next(
            (provider for provider in providers_data.values() if provider.provider_name == llm.provider_name), None
        )
        return {
            "model": llm.code,
            "model_provider": "openai",
            "base_url": provider.base_url,
            "api_key": provider.api_key,
            "extra_body": llm.extra_body,
        }
    except Exception as e:
        print(f"Error getting LLM params for {model_name}: {e}")
        return None
