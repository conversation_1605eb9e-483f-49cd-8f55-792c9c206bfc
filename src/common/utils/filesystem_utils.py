from pathlib import Path

from langgraph.config import get_config


class FileSystemUtils:
    @staticmethod
    def get_workspace_path() -> Path:
        """从 Config(configurable.workspace_path) 读取工作区路径，缺省为项目根下 workspace。"""
        cfg = get_config() or {}
        raw = (cfg.get("configurable") or {}).get("workspace_path")
        p = raw if isinstance(raw, Path) else (Path(raw) if raw else (Path.cwd() / "workspace"))
        if not p.exists():
            p.mkdir(parents=True, exist_ok=True)
        return p

    @staticmethod
    def write_content_to_workspace(relative_file_path: str, content: str, mode: str = "w") -> None:
        target_path = FileSystemUtils.get_workspace_path() / relative_file_path
        if not target_path.parent.exists():
            target_path.parent.mkdir(parents=True, exist_ok=True)
        # 尊重传入的写入模式（默认覆盖写，支持追加写入）
        with open(target_path, mode) as f:
            f.write(content)

    @staticmethod
    def get_full_path(relative_file_path: str) -> Path:
        target_path = FileSystemUtils.get_workspace_path() / relative_file_path
        return target_path

    @staticmethod
    def delete_file_from_workspace(relative_file_path: str) -> None:
        target_file_path = FileSystemUtils.get_workspace_path() / relative_file_path
        if target_file_path.exists():
            target_file_path.unlink()

    @staticmethod
    def read_content_from_workspace(relative_file_path: str) -> str:
        with open(FileSystemUtils.get_workspace_path() / relative_file_path, "r") as f:
            content = f.read()
            return content
