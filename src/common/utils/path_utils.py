import os
from pathlib import Path
from typing import Optional


PROJECT_ROOT = Path(__file__).resolve().parent.parent.parent.parent
LOG_DIR = PROJECT_ROOT / "log"
WORKSPACE_DIR = PROJECT_ROOT / "workspace"


def get_project_root() -> Path:
    assert PROJECT_ROOT.exists()
    return PROJECT_ROOT


def get_log_root() -> Path:
    os.makedirs(LOG_DIR, exist_ok=True)
    return LOG_DIR


def get_workspace_root(sub_dir: Optional[str] = None) -> Path:
    if sub_dir is None:
        os.makedirs(WORKSPACE_DIR, exist_ok=True)
        return WORKSPACE_DIR
    os.makedirs(WORKSPACE_DIR / sub_dir, exist_ok=True)
    return WORKSPACE_DIR / sub_dir


if __name__ == "__main__":
    print(f"{get_project_root()=}")
    print(f"{get_log_root()=}")
    print(f"{get_workspace_root()=}")
    print(f"{get_workspace_root(sub_dir='test1')=}")
    print(f"{get_workspace_root(sub_dir='test2')=}")
    print(f"{get_workspace_root(sub_dir='test3/test3')=}")
