from typing import Any, Dict


def _safe_preview_text(text: Any, limit: int) -> str:
    """安全地截取文本预览，移除换行/制表符，保证单行展示。"""
    try:
        s = str(text).replace("\n", " ").replace("\t", " ")
        return s[:limit] + ("..." if len(s) > limit else "")
    except Exception:
        return "-"


def format_langgraph_debug_line(event_value: Dict[str, Any], *, preview_len: int = 400) -> str:
    """将 langgraph debug 事件格式化为单行、按顺序、最少空格的字符串。"""
    step = event_value.get("step", "-")
    event_type = event_value.get("type", "-")
    payload = event_value.get("payload", {}) or {}

    # 统一抽取字段
    name = None
    source = None
    next_nodes = None
    triggers = None
    error_text = None

    if event_type == "checkpoint":
        source = (payload.get("metadata") or {}).get("source")
        next_nodes = payload.get("next")
    elif event_type == "task":
        name = payload.get("name")
        triggers = payload.get("triggers")
    elif event_type == "task_result":
        name = payload.get("name")
        err = payload.get("error")
        error_text = "None" if err is None else err

    # 生成行（顺序：step type 以及针对不同事件的最少字段）
    parts = [f"step.{step}", str(event_type)]

    if event_type == "checkpoint":
        if source is not None:
            parts.append(f"source=`{_safe_preview_text(source, preview_len)}`")
        if next_nodes is not None:
            parts.append(f"next=`{_safe_preview_text(next_nodes, preview_len)}`")
    elif event_type == "task":
        if name is not None:
            parts.append(f"name=`{_safe_preview_text(name, preview_len)}`")
        if triggers is not None:
            parts.append(f"triggers=`{_safe_preview_text(triggers, preview_len)}`")
    elif event_type == "task_result":
        if name is not None:
            parts.append(f"name=`{_safe_preview_text(name, preview_len)}`")
        if error_text is not None:
            parts.append(f"error=`{_safe_preview_text(error_text, preview_len)}`")
    else:
        # 未知类型尽量原样输出 payload 的简短预览
        parts.append(f"payload=`{_safe_preview_text(payload, preview_len)}`")

    return " ".join(parts)
