"""
LLM模型配置文件

用户可以直接修改这个文件中的配置来调整模型选择策略。
配置格式：
- name: 模型名称（字符串，对应 src/common/llms.py 中的常量值）
- weight: 权重，用于同级模型的随机选择（数值越大被选中概率越高）

模型名称参考（src/common/llms.py中的常量值）：
- deepseek-reasoner (deepseek_r1)
- deepseek-chat (deepseek_chat)
- kimi-k2-turbo-preview (kimi_k2_turbo)
- glm-4.5 (glm_4p5)
- qwen-turbo-2025-04-28 (turbo_llm)
- qwen-plus-2025-04-28 (plus_llm)
- qwen3-235b-a22b-instruct-2507 (qwen3_instruct)
- qwen3-235b-a22b-thinking-2507 (qwen3_thinking)
- qwen3-30b-a3b-instruct-2507 (qwen3_instruct_30b)
- qwen3-30b-a3b-thinking-2507 (qwen3_thinking_30b)
"""

# deepseek
deepseek_r1 = "deepseek-reasoner"
deepseek_r1_ppio = "deepseek-reasoner-ppio"  # 类型解析有点问题
deepseek_r1_ali = "deepseek-reasoner-ali"
deepseek_chat = "deepseek-chat"

# kimi
kimi_k2_ppio = "kimi-k2-ppio"
kimi_k2_turbo = "kimi-k2-turbo-preview"

# glm
glm_4p5_air = "glm-4.5-air"  # glm的官方api jsonmode有问题
glm_4p5 = "glm-4.5"  # glm的官方api jsonmode有问题
glm_4p5_ppio = "glm-4.5-ppio"  # ppio的封装 jsonmode没问题 (会不会是think开关的问题)

# qwen-ali
old_llm = "qwen-turbo-0919"
turbo_llm = "qwen-turbo-2025-04-28"
plus_llm = "qwen-plus-2025-04-28"

# qwen-open
qwen3_instruct_ppio = "qwen3-235b-a22b-instruct-2507-ppio"  # 有时候乱码
qwen3_thinking_ppio = "qwen3-235b-a22b-thinking-2507-ppio"  # 这个不支持json mode 有时候乱码？

qwen3_instruct = "qwen3-235b-a22b-instruct-2507"
qwen3_thinking = "qwen3-235b-a22b-thinking-2507"  # 这个不支持json mode

qwen3_instruct_30b = "qwen3-30b-a3b-instruct-2507"
qwen3_thinking_30b = "qwen3-30b-a3b-thinking-2507"  # 这个不支持json mode


# turbo升级版flash
qwen_flash = "qwen-flash"
qwen_flash_thinking = "qwen-flash:thinking"

# select
book_chat = qwen_flash
book_thinking = qwen_flash_thinking

book_chat_plus = qwen3_instruct_30b
book_thinking_plus = qwen3_thinking_30b

book_chat_pro = qwen3_instruct
book_thinking_pro = qwen3_thinking


# LLM配置 - 用户可以直接修改这里的配置
LLM_CONFIG = {
    "json_mode": {
        "low": [
            {"name": book_chat, "weight": 1.0},
        ],
        "medium": [
            {"name": book_chat_plus, "weight": 1.0},
        ],
        "high": [
            {"name": book_chat_pro, "weight": 1.0},
        ],
    },
    "text_mode": {
        "low": [
            {"name": qwen_flash_thinking, "weight": 1.0},
        ],
        "medium": [
            {"name": book_thinking_plus, "weight": 1.0},
        ],
        "high": [
            {"name": book_thinking_pro, "weight": 1.0},
        ],
    },
}

# 配置说明
CONFIG_DESCRIPTION = {
    "json_mode": "用于结构化输出的模型配置（如JSON、Pydantic对象等）",
    "text_mode": "用于文本生成的模型配置（如文章、对话等）",
    "tiers": {
        "low": "低级模型：快速、便宜，适合简单任务",
        "medium": "中级模型：平衡性能和成本，适合一般任务",
        "high": "高级模型：最高质量，适合复杂任务",
    },
    "weight": "权重：数值越大被选中概率越高，支持小数",
}


def get_config_description():
    """获取配置说明"""
    return CONFIG_DESCRIPTION


def validate_config():
    """验证配置的有效性"""
    errors = []

    for mode_name, mode_config in LLM_CONFIG.items():
        if mode_name not in ["json_mode", "text_mode"]:
            errors.append(f"无效的模式名称: {mode_name}")
            continue

        for tier_name, tier_models in mode_config.items():
            if tier_name not in ["low", "medium", "high"]:
                errors.append(f"无效的等级名称: {tier_name} in {mode_name}")
                continue

            if not isinstance(tier_models, list):
                errors.append(f"{mode_name}.{tier_name} 必须是列表")
                continue

            for i, model_info in enumerate(tier_models):
                if not isinstance(model_info, dict):
                    errors.append(f"{mode_name}.{tier_name}[{i}] 必须是字典")
                    continue

                if "name" not in model_info:
                    errors.append(f"{mode_name}.{tier_name}[{i}] 缺少 'name' 字段")

                if "weight" not in model_info:
                    errors.append(f"{mode_name}.{tier_name}[{i}] 缺少 'weight' 字段")
                elif not isinstance(model_info["weight"], (int, float)) or model_info["weight"] <= 0:
                    errors.append(f"{mode_name}.{tier_name}[{i}] 'weight' 必须是正数")

    return errors


def print_config():
    """打印当前配置"""
    print("=== LLM模型配置 ===")
    print(f"JSON模式: {CONFIG_DESCRIPTION['json_mode']}")
    print(f"TEXT模式: {CONFIG_DESCRIPTION['text_mode']}")
    print()

    for mode_name, mode_config in LLM_CONFIG.items():
        print(f"{mode_name.upper()}:")
        for tier_name, tier_models in mode_config.items():
            tier_desc = CONFIG_DESCRIPTION["tiers"][tier_name]
            print(f"  {tier_name} ({tier_desc}):")
            for model_info in tier_models:
                print(f"    - {model_info['name']} (权重: {model_info['weight']})")
        print()


if __name__ == "__main__":
    # 验证配置
    errors = validate_config()
    if errors:
        print("❌ 配置验证失败:")
        for error in errors:
            print(f"  - {error}")
    else:
        print("✅ 配置验证通过")
        print_config()
