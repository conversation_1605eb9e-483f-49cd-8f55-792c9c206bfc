"""
LLM配置系统 - 基于模式的多级重试架构

设计原则：
1. 模式分类：json_mode 和 text_mode
2. 多级优先级：低级 -> 中级 -> 高级
3. 同级多模型：支持同一级别配置多个模型
4. 用户可配置：提供清晰的配置接口
"""

import logging
import random
from dataclasses import dataclass, field
from enum import Enum
from typing import Dict, List, Optional, Union

logger = logging.getLogger(__name__)


class RequestMode(Enum):
    """请求模式枚举"""

    JSON_MODE = "json_mode"  # 结构化输出请求
    TEXT_MODE = "text_mode"  # 文本输出请求


class ModelTier(Enum):
    """模型等级枚举"""

    LOW = "low"  # 低级模型：快速、便宜
    MEDIUM = "medium"  # 中级模型：平衡性能和成本
    HIGH = "high"  # 高级模型：最高质量


@dataclass
class ModelConfig:
    """单个模型配置"""

    name: str
    weight: float = 1.0  # 同级模型的权重，用于负载均衡

    def __post_init__(self):
        if self.weight <= 0:
            raise ValueError(f"Model weight must be positive, got {self.weight}")


@dataclass
class TierConfig:
    """等级配置，包含该等级的所有模型"""

    models: List[ModelConfig] = field(default_factory=list)

    def add_model(self, name: str, weight: float = 1.0) -> None:
        """添加模型到该等级"""
        self.models.append(ModelConfig(name=name, weight=weight))

    def get_random_model(self) -> Optional[str]:
        """根据权重随机选择一个模型"""
        if not self.models:
            return None

        if len(self.models) == 1:
            return self.models[0].name

        # 权重随机选择
        weights = [model.weight for model in self.models]
        selected = random.choices(self.models, weights=weights, k=1)[0]
        return selected.name

    def get_all_models(self) -> List[str]:
        """获取该等级的所有模型名称"""
        return [model.name for model in self.models]


@dataclass
class ModeConfig:
    """模式配置，包含该模式下的所有等级"""

    low: TierConfig = field(default_factory=TierConfig)
    medium: TierConfig = field(default_factory=TierConfig)
    high: TierConfig = field(default_factory=TierConfig)

    def get_tier_config(self, tier: ModelTier) -> TierConfig:
        """获取指定等级的配置"""
        if tier == ModelTier.LOW:
            return self.low
        elif tier == ModelTier.MEDIUM:
            return self.medium
        elif tier == ModelTier.HIGH:
            return self.high
        else:
            raise ValueError(f"Unknown tier: {tier}")

    def get_model_for_tier(self, tier: ModelTier) -> Optional[str]:
        """获取指定等级的模型"""
        tier_config = self.get_tier_config(tier)
        return tier_config.get_random_model()

    def get_retry_sequence(self) -> List[ModelTier]:
        """获取重试序列：低级 -> 中级 -> 高级"""
        sequence = []
        if self.low.models:
            sequence.append(ModelTier.LOW)
        if self.medium.models:
            sequence.append(ModelTier.MEDIUM)
        if self.high.models:
            sequence.append(ModelTier.HIGH)
        return sequence


class LLMConfigManager:
    """LLM配置管理器"""

    def __init__(self):
        self.configs: Dict[RequestMode, ModeConfig] = {
            RequestMode.JSON_MODE: ModeConfig(),
            RequestMode.TEXT_MODE: ModeConfig(),
        }
        # 每个等级内的连续尝试次数，之后才升级到下一等级
        self.retries_per_tier: int = 3
        self._setup_default_config()

    def _setup_default_config(self):
        """从JSON配置文件加载默认配置"""
        try:
            # 导入配置
            from src.common.llm_models_config import LLM_CONFIG, validate_config

            # 验证配置
            errors = validate_config()
            if errors:
                logger.error(f"LLM配置验证失败: {errors}")
                raise ValueError(f"LLM配置验证失败: {errors}")

            # 加载JSON模式配置
            json_config = self.configs[RequestMode.JSON_MODE]
            for tier_name, models in LLM_CONFIG["json_mode"].items():
                tier_config = getattr(json_config, tier_name)
                tier_config.models.clear()  # 清空现有配置
                for model_info in models:
                    tier_config.add_model(model_info["name"], model_info["weight"])

            # 加载TEXT模式配置
            text_config = self.configs[RequestMode.TEXT_MODE]
            for tier_name, models in LLM_CONFIG["text_mode"].items():
                tier_config = getattr(text_config, tier_name)
                tier_config.models.clear()  # 清空现有配置
                for model_info in models:
                    tier_config.add_model(model_info["name"], model_info["weight"])

            logger.info("LLM配置加载成功")

        except Exception as e:
            logger.error(f"加载LLM配置失败: {e}")
            # 如果配置加载失败，使用硬编码的备用配置
            self._setup_fallback_config()

    def _setup_fallback_config(self):
        """设置备用配置（硬编码）"""
        logger.warning("使用备用LLM配置")

        # JSON模式配置
        json_config = self.configs[RequestMode.JSON_MODE]
        json_config.low.add_model("qwen3-30b-a3b-instruct-2507", weight=1.0)
        json_config.medium.add_model("qwen3-235b-a22b-instruct-2507", weight=1.0)
        json_config.high.add_model("deepseek-chat", weight=1.0)

        # TEXT模式配置
        text_config = self.configs[RequestMode.TEXT_MODE]
        text_config.low.add_model("qwen3-30b-a3b-thinking-2507", weight=1.0)
        text_config.medium.add_model("qwen3-235b-a22b-thinking-2507", weight=1.0)
        text_config.high.add_model("deepseek-reasoner", weight=1.0)

    def get_model_for_request(
        self, mode: RequestMode, tier: Optional[ModelTier] = ModelTier.LOW, retry_index: int = 0
    ) -> Optional[str]:
        """
        获取请求使用的模型

        Args:
            mode: 请求模式
            tier: 指定的模型等级（可选）
            retry_index: 重试索引，用于自动升级等级

        Returns:
            模型名称，如果没有可用模型则返回None
        """
        mode_config = self.configs[mode]
        retry_sequence = mode_config.get_retry_sequence()

        if not retry_sequence:
            logger.warning(f"No models configured for mode: {mode.value}")
            return None

        # 允许tier为None，默认从LOW开始
        start_tier: ModelTier = tier or ModelTier.LOW

        # 从当前tier开始的索引
        try:
            start_index = retry_sequence.index(start_tier)
        except ValueError:
            # 如果配置中缺少指定的tier，则从序列起点开始
            logger.warning(
                f"Requested start tier {start_tier.value} not available in {mode.value} config; falling back to {retry_sequence[0].value}"
            )
            start_index = 0

        # 计算需要升级的等级步数：每个等级内部尝试retries_per_tier次
        tier_offset = retry_index // max(1, self.retries_per_tier)
        actual_index = start_index + tier_offset

        if actual_index >= len(retry_sequence):
            logger.warning(
                f"No more models available after retry #{retry_index} (retries_per_tier={self.retries_per_tier}) in {mode.value} mode"
            )
            return None

        actual_tier = retry_sequence[actual_index]
        model = mode_config.get_model_for_tier(actual_tier)
        if model:
            if retry_index > 0:
                logger.info(
                    f"Retry #{retry_index}: Using {actual_tier.value} tier model (retries_per_tier={self.retries_per_tier}): {model}"
                )
            return model

        logger.warning(f"No model found for tier {actual_tier.value} on retry #{retry_index} in {mode.value} mode")
        return None

    def get_max_retries(self, mode: RequestMode) -> int:
        """获取指定模式的最大重试次数（不含第一次尝试）

        计算方式：可用等级数量 × 每等级尝试次数 - 1
        举例：3个等级、每等级3次 => 3×3-1=8（循环会从0到8，共9次尝试）
        """
        mode_config = self.configs[mode]
        tiers_count = len(mode_config.get_retry_sequence())
        if tiers_count == 0:
            return 0
        return tiers_count * max(1, self.retries_per_tier) - 1

    def set_retries_per_tier(self, retries: int) -> None:
        """设置每个等级内的连续尝试次数"""
        if retries < 1:
            raise ValueError(f"retries_per_tier must be >= 1, got {retries}")
        self.retries_per_tier = retries

    def configure_mode(self, mode: RequestMode, tier: ModelTier, models: List[Union[str, tuple]]) -> None:
        """
        配置指定模式和等级的模型

        Args:
            mode: 请求模式
            tier: 模型等级
            models: 模型列表，可以是字符串或(name, weight)元组
        """
        mode_config = self.configs[mode]
        tier_config = mode_config.get_tier_config(tier)

        # 清空现有配置
        tier_config.models.clear()

        # 添加新配置
        for model in models:
            if isinstance(model, str):
                tier_config.add_model(model)
            elif isinstance(model, tuple) and len(model) == 2:
                name, weight = model
                tier_config.add_model(name, weight)
            else:
                raise ValueError(f"Invalid model format: {model}")

        logger.info(f"Configured {mode.value} {tier.value} tier with {len(models)} models")


# 全局配置管理器实例
llm_config_manager = LLMConfigManager()


def get_model_for_request(mode: RequestMode, tier: ModelTier = ModelTier.LOW, retry_index: int = 0) -> Optional[str]:
    """便捷函数：获取请求使用的模型"""
    return llm_config_manager.get_model_for_request(mode, tier, retry_index)


def get_max_retries(mode: RequestMode) -> int:
    """便捷函数：获取最大重试次数"""
    return llm_config_manager.get_max_retries(mode)


def configure_models(mode: RequestMode, tier: ModelTier, models: List[Union[str, tuple]]) -> None:
    """便捷函数：配置模型"""
    llm_config_manager.configure_mode(mode, tier, models)


# 用户友好的配置函数
def configure_json_models(
    low: List[Union[str, tuple]] = None, medium: List[Union[str, tuple]] = None, high: List[Union[str, tuple]] = None
) -> None:
    """
    配置JSON模式的模型

    Args:
        low: 低级模型列表
        medium: 中级模型列表
        high: 高级模型列表

    Example:
        configure_json_models(
            low=["qwen3-30b-a3b-instruct-2507", ("qwen-turbo-2025-04-28", 0.8)],
            medium=["qwen3-235b-a22b-instruct-2507"],
            high=["deepseek-chat", "glm-4.5"]
        )
    """
    if low is not None:
        configure_models(RequestMode.JSON_MODE, ModelTier.LOW, low)
    if medium is not None:
        configure_models(RequestMode.JSON_MODE, ModelTier.MEDIUM, medium)
    if high is not None:
        configure_models(RequestMode.JSON_MODE, ModelTier.HIGH, high)


def configure_text_models(
    low: List[Union[str, tuple]] = None, medium: List[Union[str, tuple]] = None, high: List[Union[str, tuple]] = None
) -> None:
    """
    配置TEXT模式的模型

    Args:
        low: 低级模型列表
        medium: 中级模型列表
        high: 高级模型列表

    Example:
        configure_text_models(
            low=["qwen3-30b-a3b-thinking-2507", ("qwen3-30b-a3b-instruct-2507", 0.8)],
            medium=["qwen3-235b-a22b-thinking-2507"],
            high=["deepseek-reasoner", "kimi-k2-turbo-preview"]
        )
    """
    if low is not None:
        configure_models(RequestMode.TEXT_MODE, ModelTier.LOW, low)
    if medium is not None:
        configure_models(RequestMode.TEXT_MODE, ModelTier.MEDIUM, medium)
    if high is not None:
        configure_models(RequestMode.TEXT_MODE, ModelTier.HIGH, high)


def get_config_summary() -> Dict:
    """获取当前配置摘要"""
    summary = {}
    for mode in RequestMode:
        mode_config = llm_config_manager.configs[mode]
        summary[mode.value] = {
            "low": mode_config.low.get_all_models(),
            "medium": mode_config.medium.get_all_models(),
            "high": mode_config.high.get_all_models(),
            "max_retries": llm_config_manager.get_max_retries(mode),
        }
    return summary


def print_config_summary() -> None:
    """打印当前配置摘要"""
    summary = get_config_summary()
    print("\n=== LLM Configuration Summary ===")
    for mode, config in summary.items():
        print(f"\n{mode.upper()} Mode (max_retries: {config['max_retries']}):")
        for tier in ["low", "medium", "high"]:
            models = config[tier]
            if models:
                print(f"  {tier.capitalize()}: {', '.join(models)}")
            else:
                print(f"  {tier.capitalize()}: (empty)")
    print("=" * 35)
