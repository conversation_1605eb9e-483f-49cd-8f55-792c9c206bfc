from dataclasses import dataclass


CHINESE_LANGUAGE_LINE = "工作语言：中文"

# Shared constraints for character usage in scenes/chapters
CHARACTER_USAGE_CONSTRAINTS = (
    "- 只能使用角色设定/相关角色设定中已明确设定的角色\n"
    "- 严禁创造或引入设定集中没有的新角色\n"
    "- 龙套角色、路人甲乙、临时出现的小角色等均不应出现在内容中\n"
    "- 章节与场景应专注于已设定角色的互动和发展"
)

# Global writing guidance for xuanhuan爽文 style
WRITER_ROLE_SYSTEM = (
    "你是一位专业的玄幻爽文网络小说作家，擅长长篇连续创作，"
    "能够在保证信息密度与节奏的同时，维持爽点与冲突推进。"
)

# Stream writing specific guidance
STREAM_WRITING_GUIDANCE = (
    "- 不按固定章节生成，先持续生成高质量的连续内容\n"
    "- 正文先进入缓冲区，后续再进行智能分章与断章润色"
)


@dataclass(frozen=True)
class SectionNames:
    header: str = "HEADER"
    instruction: str = "INSTRUCTION"
    setting: str = "SETTING"
    characters: str = "CHARACTERS"
    scene: str = "SCENE"
    outline: str = "OUTLINE"
    buffer: str = "RECENT_BUFFER"
    constraints: str = "CONSTRAINTS"
    json_schema: str = "JSON_SCHEMA"
    previous_summary: str = "PREVIOUS_SUMMARY"
    demo: str = "DEMO"

