"""
统一上下文构建器：用于所有LLM系统消息的结构化拼装
可选：未来可扩展 token 预算、检索片段化、模型特定指令等
"""

import datetime
import json
from dataclasses import dataclass
from typing import Any, Optional

from langchain_core.messages import BaseMessage, HumanMessage, SystemMessage

from src.common.utils.token_utils import TokenTools
from src.context.policies import CHARACTER_USAGE_CONSTRAINTS, CHINESE_LANGUAGE_LINE, SectionNames, WRITER_ROLE_SYSTEM


@dataclass
class RetrievalChunk:
    title: str
    content: str


def _time_str() -> str:
    return datetime.datetime.now().strftime("%Y-%m-%d %H:%M")


class ContextBuilder:
    """
    统一构建LLM上下文，采用消息分层：
    - SystemMessage：固定格式要求、通用设定（语言、角色、通用约束）、JSON Schema
    - HumanMessage：业务相关任务与上下文（设定集内容、角色/大纲/场景等动态数据）

    同时支持 token 预算：优先裁剪 Human 部分，保证 System（尤其是 Schema）稳定。
    """

    def __init__(self) -> None:
        # 系统侧固定内容（不会频繁变化的指导、格式、schema 等）
        self._system_blocks: list[str] = []
        # 业务侧内容（任务与上下文）
        self._human_blocks: list[str] = []
        # 独立的 schema 文本块（拼到 SystemMessage 中）
        self._schema_block: Optional[str] = None

    def header(self, current_time: Optional[str] = None) -> "ContextBuilder":
        """
        构建稳定的系统前缀。

        说明：
        - 为了最大化厂商侧前缀缓存命中率，禁止在 SystemMessage 中引入动态时间等变化内容。
        - 如确需展示时间，请放入 HumanMessage 的非首部区域。
        - 保留语言与角色设定等稳定信息。
        """
        parts = [
            CHINESE_LANGUAGE_LINE,
            "",
            WRITER_ROLE_SYSTEM,
        ]
        self._system_blocks.append("\n".join(parts))
        return self

    def section(self, title: str, content: Optional[str]) -> "ContextBuilder":
        if content is None:
            return self
        block = [f"<{title}>", content, f"</{title}>"]
        self._human_blocks.append("\n".join(block))
        return self

    def constraints(self, extra: Optional[str] = None) -> "ContextBuilder":
        blocks = ["**重要约束**:", CHARACTER_USAGE_CONSTRAINTS]
        if extra:
            blocks.append(extra)
        self._system_blocks.append("\n".join(blocks))
        return self

    def json_schema(self, schema_json: Any) -> "ContextBuilder":
        schema_text = (
            schema_json if isinstance(schema_json, str) else json.dumps(schema_json, ensure_ascii=False, indent=2)
        )
        block = [
            "**输出格式**:",
            "严格只输出一个JSON对象，不得包含任何额外文字/解释。",
            "不要使用Markdown代码块（例如 ``` 或 ```json）。",
            "不要复制/粘贴下面的schema本体；必须输出符合该schema的数据对象。",
            "schema如下（仅供参考，请勿直接返回schema）：",
            "```",
            schema_text,
            "```",
        ]
        # schema 放入系统消息
        self._schema_block = "\n".join(block)
        return self

    @staticmethod
    def anti_schema_messages() -> list[BaseMessage]:
        """当模型出现 schema 回显倾向时，建议追加到提示里的系统/人类约束消息。"""
        return [
            SystemMessage(
                content=(
                    "严格只输出一个JSON数据对象；不要返回或复制schema文本；"
                    "不要使用任何Markdown代码块（如```或```json）；不要输出解释性文字。"
                )
            ),
            HumanMessage(content="请给我内容，而不是schema"),
        ]

    def demo(self, demo_text: Optional[str], note: Optional[str] = None) -> "ContextBuilder":
        if not demo_text:
            return self
        header = (
            "--------------------------------\n示例/参考内容如下，请参考风格与节奏\n--------------------------------"
        )
        if note:
            header += f"\n{note}"
        block = [header, "<demo>", demo_text, "</demo>"]
        self._human_blocks.append("\n".join(block))
        return self

    def attach_retrieval(self, chunks: Optional[list[RetrievalChunk]], max_chars: int = 4000) -> "ContextBuilder":
        """附加检索到的片段集合（轻量拼装，不引入外部依赖）"""
        if not chunks:
            return self
        acc = []
        used = 0
        for ch in chunks:
            block = f"<{ch.title}>\n{ch.content}\n</{ch.title}>"
            if used + len(block) > max_chars:
                break
            acc.append(block)
            used += len(block)
        if acc:
            self._human_blocks.append("\n\n".join(acc))
        return self

    def previous_summary(self, content: Optional[str]) -> "ContextBuilder":
        return self.section(SectionNames.previous_summary, content)

    def build(self, max_tokens: Optional[int] = None, policy: Optional[str] = None) -> list[BaseMessage]:
        # 初始拼装
        def join_blocks(blocks: list[str]) -> str:
            return "\n\n".join([s for s in blocks if s and s.strip()])

        stable_system_text = join_blocks(self._system_blocks)
        schema_text = self._schema_block or ""
        human_text = join_blocks(self._human_blocks)

        if not max_tokens:
            messages: list[BaseMessage] = []
            if stable_system_text:
                messages.append(SystemMessage(content=stable_system_text))
            if schema_text:
                messages.append(SystemMessage(content=schema_text))
            if human_text:
                messages.append(HumanMessage(content=human_text))
            return messages or [SystemMessage(content="")]  # 兜底

        # 估算token并按预算截断正文，保留schema
        def est_pair(sys_txt: str, hum_txt: str, schema_txt: str) -> int:
            msgs: list[BaseMessage] = []
            if sys_txt:
                msgs.append(SystemMessage(content=sys_txt))
            if schema_txt:
                msgs.append(SystemMessage(content=schema_txt))
            if hum_txt:
                msgs.append(HumanMessage(content=hum_txt))
            return TokenTools.count_messages_approximately_tokens(msgs)

        if est_pair(stable_system_text, human_text, schema_text) <= max_tokens:
            msgs: list[BaseMessage] = []
            if stable_system_text:
                msgs.append(SystemMessage(content=stable_system_text))
            if schema_text:
                msgs.append(SystemMessage(content=schema_text))
            if human_text:
                msgs.append(HumanMessage(content=human_text))
            return msgs or [SystemMessage(content="")]  # 兜底

        # 优先裁剪 Human 块；System（含 schema）尽量保留
        kept_human_blocks = list(self._human_blocks)

        def remove_block_if(predicate):
            nonlocal kept_human_blocks
            trimmed = [b for b in kept_human_blocks if not predicate(b)]
            if len(trimmed) != len(kept_human_blocks):
                kept_human_blocks = trimmed
                return True
            return False

        # 先移除 demo
        while est_pair(
            stable_system_text, join_blocks(kept_human_blocks), schema_text
        ) > max_tokens and remove_block_if(lambda b: "<demo>" in b or "</demo>" in b):
            pass

        # 再移除历史摘要
        while est_pair(
            stable_system_text, join_blocks(kept_human_blocks), schema_text
        ) > max_tokens and remove_block_if(lambda b: "<PREVIOUS_SUMMARY>" in b):
            pass

        # 按标题优先级，移除可选分节（基于policy）
        def get_title(block: str) -> str:
            if block.startswith("<") and ">" in block.splitlines()[0]:
                first = block.splitlines()[0]
                return first.strip().lstrip("<").rstrip(">")
            return ""

        policy_optional_map: dict[str, list[str]] = {
            # 场景：优先保留任务正文，先移除demo/NOTE/书籍信息
            "scene": [
                "NOTE",
                "书籍信息",
                "相关角色设定",
                "当前卷大纲（摘要）",
            ],
            # 大纲：任务正文最重要，其余均可退化
            "outline": [
                "NOTE",
                "书籍信息",
                "相关角色设定",
                "当前场景信息",
                "当前卷大纲（摘要）",
            ],
            # 设定相关
            "bs_planner": ["NOTE", "书籍信息"],
            "bs_steps": ["NOTE", "书籍信息"],
            # 流写
            "stream": [
                "NOTE",
                "书籍信息",
                "相关角色设定",
                "卷级核心摘要（短）",
                "当前卷大纲（摘要）",
                "未解悬念清单（短）",
                "最近缓冲区内容（避免重复衔接，仅参考，不要复述）",
            ],
            # 角色
            "character": ["NOTE", "书籍信息"],
        }
        optional_titles = policy_optional_map.get(
            policy or "",
            [
                "相关角色设定",
                "当前卷大纲（摘要）",
                "当前场景信息",
                "最近缓冲区内容（避免重复衔接，仅参考，不要复述）",
                "书籍信息",
                "NOTE",
            ],
        )
        for title in optional_titles:
            while est_pair(
                stable_system_text, join_blocks(kept_human_blocks), schema_text
            ) > max_tokens and remove_block_if(lambda b, t=title: get_title(b) == t):
                pass

        # 若依然超预算，对剩余正文进行渐进截断
        base_human_text = join_blocks(kept_human_blocks)
        if not base_human_text:
            # 只剩 System（含 schema）
            msgs: list[BaseMessage] = []
            if stable_system_text:
                msgs.append(SystemMessage(content=stable_system_text))
            if schema_text:
                msgs.append(SystemMessage(content=schema_text))
            return msgs or [SystemMessage(content="")]

        keep_ratio = 0.9
        for _ in range(12):
            if est_pair(stable_system_text, base_human_text, schema_text) <= max_tokens:
                msgs: list[BaseMessage] = [SystemMessage(content=stable_system_text)] if stable_system_text else []
                if schema_text:
                    msgs.append(SystemMessage(content=schema_text))
                msgs.append(HumanMessage(content=base_human_text))
                return msgs
            new_len = max(500, int(len(base_human_text) * keep_ratio))
            base_human_text = base_human_text[:new_len]
            keep_ratio = max(0.6, keep_ratio - 0.05)

        msgs: list[BaseMessage] = [SystemMessage(content=stable_system_text)] if stable_system_text else []
        if schema_text:
            msgs.append(SystemMessage(content=schema_text))
        if base_human_text:
            msgs.append(HumanMessage(content=base_human_text))
        return msgs or [SystemMessage(content="")]
