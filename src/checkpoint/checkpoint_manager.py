from __future__ import annotations

import shutil

from contextlib import asynccontextmanager

from dataclasses import dataclass, field
from pathlib import Path
from typing import Any, AsyncIterator, Dict, List, Optional, Tuple

# 已移除：进度快照命名空间（snapshot 功能废弃）

from src.common.utils.time_utils import format_human_datetime
from src.memory.store_utils import get_store_and_checkpointer
from src.nodes.common.store_manager import WriterStoreManager


def _safe_get(obj: Any, key: str, default: Any = None) -> Any:
    if obj is None:
        return default
    if isinstance(obj, dict):
        return obj.get(key, default)
    return getattr(obj, key, default)


def _find_in_obj(obj: Any, keys: List[str]) -> Optional[Any]:
    try:
        if isinstance(obj, dict):
            for k in keys:
                if k in obj and obj[k] is not None:
                    return obj[k]
            for v in obj.values():
                found = _find_in_obj(v, keys)
                if found is not None:
                    return found
    except Exception:
        pass
    return None


# 已移除：_has_key_deep（废弃，未使用）

# 统一解析工具：收敛 thread_id / book_id / book_name 的提取逻辑，避免各处重复且不一致


def _extract_thread_id(rec: Any) -> Optional[str]:
    tid = _safe_get(rec, "thread_id")
    if not tid:
        cfg = _safe_get(rec, "config")
        configurable = _safe_get(cfg, "configurable")
        if isinstance(configurable, dict):
            tid = configurable.get("thread_id")
        elif configurable is not None:
            tid = _safe_get(configurable, "thread_id")
        if not tid and isinstance(cfg, dict):
            tid = cfg.get("thread_id")
    return tid


def _extract_book_info(checkpoint: Any, rec: Any) -> Tuple[Optional[str], Optional[str]]:
    """返回 (book_id, book_name)；尽可能从 values/state/metadata 与 rec.metadata 中补齐"""
    book_id: Optional[str] = None
    book_name: Optional[str] = None
    try:
        if isinstance(checkpoint, dict):
            values = checkpoint.get("values") or checkpoint.get("state") or checkpoint
            book_id = _find_in_obj(values, ["book_id"]) or book_id
            book_name = _find_in_obj(values, ["book_name"]) or book_name
            meta = checkpoint.get("metadata") if isinstance(checkpoint, dict) else None
            if isinstance(meta, dict):
                book_id = meta.get("book_id") or book_id
                book_name = meta.get("book_name") or book_name
        rec_meta = _safe_get(rec, "metadata") or (rec.get("metadata") if isinstance(rec, dict) else None)
        if isinstance(rec_meta, dict):
            book_id = rec_meta.get("book_id") or book_id
            book_name = rec_meta.get("book_name") or book_name
    except Exception:
        pass
    return book_id, book_name


@dataclass
class BookGroupSummary:
    book_id: str
    book_name: Optional[str]
    created_ts: Optional[str]
    updated_ts: Optional[str]
    representative_thread_id: str
    thread_ids: List[str]
    meta: Dict[str, Any] = field(default_factory=dict)


@dataclass
class CheckpointSummary:
    checkpoint_id: str
    thread_id: str
    book_id: str
    ts: Optional[str]
    metadata: Dict[str, Any]
    parent_checkpoint_id: Optional[str] = None


class CheckpointManager:
    """
    LangGraph Checkpoint 封装：以 book_id 作为一等公民进行聚合与操作。

    约定：book_id 为首次创建项目时的 thread_id；后续同一本书的不同运行分支可能拥有不同的 thread_id，
    但均在其 checkpoint/values 或 metadata 中携带相同的 book_id。
    """

    def __init__(self, store, checkpointer):
        self.store = store
        self.checkpointer = checkpointer

    async def cleanup_checkpoints_without_plan(self, *, dry_run: bool = False) -> int:
        """
        清理“尚未生成策划案(WriterPlan)”的 checkpoint 历史。

        规则：
        - 扫描全部 checkpoint，按 book_id 聚合
        - 若该 book_id 在 WriterStore 中不存在策划案，则视为“无意义历史”，
          删除该 book_id 下的所有线程(thread_id)的 checkpoint 记录

        返回删除的线程数（dry_run 时为将要删除的线程数）。
        """
        # 聚合所有书籍分组
        groups = await self.list_books()
        if not groups:
            return 0

        total_deleted_threads = 0
        for g in groups:
            bid = g.book_id
            try:
                wsm = WriterStoreManager(book_id=bid, store=self.store)
                plan = await wsm.get_writer_plan()
            except Exception:
                plan = None

            if plan is None:
                # 无策划案：删除该书所有线程的 checkpoint
                for tid in g.thread_ids:
                    total_deleted_threads += 1
                    if dry_run:
                        print(f"[Dry-Run] 将删除无策划案历史：book_id={bid}, thread_id={tid}")
                        continue
                    try:
                        await self.checkpointer.adelete_thread(tid)
                    except Exception:
                        # 静默失败，继续下一条
                        pass

        return total_deleted_threads

    @classmethod
    @asynccontextmanager
    async def connect(cls) -> AsyncIterator["CheckpointManager"]:
        async with get_store_and_checkpointer() as (store, checkpointer):
            yield cls(store, checkpointer)

    async def list_books(self) -> List[BookGroupSummary]:
        """
        扫描所有 checkpoint，按 book_id 聚合，返回书籍列表。
        - 与 thread_id 解耦：仅用于恢复时作为参数；聚合与显示均以 book_id 为准。
        - book_name 若缺失，将从 WriterPlan 中补齐。
        """
        groups_map: Dict[str, Dict[str, Any]] = {}

        async for rec in self.checkpointer.alist({}):
            # 统一解析 thread_id
            thread_id = _extract_thread_id(rec)
            if not thread_id:
                # 无法识别线程，跳过
                continue

            checkpoint = _safe_get(rec, "checkpoint", {}) or {}
            ts = checkpoint.get("ts") if isinstance(checkpoint, dict) else None

            # 统一解析 book_id / book_name
            book_id, book_name = _extract_book_info(checkpoint, rec)

            # 取得 checkpoint_id 用于计数
            cp_id = None
            try:
                if isinstance(checkpoint, dict):
                    cp_id = checkpoint.get("id") or checkpoint.get("checkpoint_id")
                if not cp_id:
                    cp_id = _safe_get(rec, "checkpoint_id")
            except Exception:
                cp_id = None

            bid = book_id or thread_id
            g = groups_map.get(bid)
            if g is None:
                g = {
                    "book_name": book_name,
                    "created_ts": ts,
                    "updated_ts": ts,
                    "rep_thread": thread_id,
                    "rep_ts": ts,
                    "threads": set([thread_id]),
                    "cp_count": 0,
                }
                groups_map[bid] = g
            # 计数：只要当前记录存在 checkpoint_id，则计为一个（不同记录可对应不同 cp）
            if cp_id:
                g["cp_count"] = (g.get("cp_count") or 0) + 1
            else:
                if not g.get("book_name") and book_name:
                    g["book_name"] = book_name
                if ts and (not g.get("created_ts") or ts < g["created_ts"]):
                    g["created_ts"] = ts
                if ts and (not g.get("updated_ts") or ts > g["updated_ts"]):
                    g["updated_ts"] = ts
                if ts and (not g.get("rep_ts") or ts > g["rep_ts"]):
                    g["rep_ts"] = ts
                    g["rep_thread"] = thread_id
                g["threads"].add(thread_id)

        # 二次补齐书名
        for bid, g in groups_map.items():
            if not g.get("book_name") and bid:
                try:
                    wsm = WriterStoreManager(book_id=bid, store=self.store)
                    plan = await wsm.get_writer_plan()
                    if plan is not None:
                        g["book_name"] = plan.book_name
                except Exception:
                    pass

        groups: List[BookGroupSummary] = []
        for bid, g in groups_map.items():
            groups.append(
                BookGroupSummary(
                    book_id=bid,
                    book_name=g.get("book_name"),
                    created_ts=g.get("created_ts"),
                    updated_ts=g.get("updated_ts"),
                    representative_thread_id=g.get("rep_thread"),
                    thread_ids=sorted(list(g.get("threads", []))),
                    meta={"checkpoint_count": g.get("cp_count") or 0},
                )
            )

        groups.sort(key=lambda gg: gg.updated_ts or "", reverse=True)
        return groups

    async def list_checkpoints(self, book_id: str, limit: Optional[int] = None) -> List[CheckpointSummary]:
        """
        列出指定 book_id 的 checkpoint（可能跨多个 thread_id）。
        - 仅基于 state['resume_info']（最新一条）作为展示信息，不再兼容旧字段
        - 新→旧排序；若提供 limit，仅返回最近 limit 条
        """
        results: List[CheckpointSummary] = []

        async for rec in self.checkpointer.alist({}):
            thread_id = _safe_get(rec, "thread_id")
            if not thread_id:
                cfg = _safe_get(rec, "config")
                configurable = _safe_get(cfg, "configurable")
                if isinstance(configurable, dict):
                    thread_id = configurable.get("thread_id")
                elif configurable is not None:
                    thread_id = _safe_get(configurable, "thread_id")
                if not thread_id and isinstance(cfg, dict):
                    thread_id = cfg.get("thread_id")
            if not thread_id:
                continue

            checkpoint = _safe_get(rec, "checkpoint", {}) or {}
            if not isinstance(checkpoint, dict):
                continue

            values = checkpoint.get("values") or checkpoint.get("state") or checkpoint
            bid, _ = _extract_book_info(checkpoint, rec)
            if not bid:
                bid = thread_id  # 回退策略：默认 thread_id 作为 book_id

            if bid != book_id:
                continue

            cp_id = checkpoint.get("id") or _safe_get(rec, "checkpoint_id") or checkpoint.get("checkpoint_id")
            if not cp_id:
                # 没有可用的 checkpoint_id，跳过
                continue

            ts = checkpoint.get("ts")
            # 从 resume_info 读取展示信息（取最后一条）
            meta = {}
            try:
                if isinstance(values, dict):
                    resume_list = values.get("resume_info")
                    if isinstance(resume_list, list) and resume_list:
                        last = resume_list[-1]
                        if isinstance(last, dict):
                            meta = last
            except Exception:
                meta = {}

            # 读取父 checkpoint id（若存在）
            parent_cp_id = (
                _safe_get(rec, "parent_checkpoint_id")
                or _safe_get(checkpoint, "parent_checkpoint_id")
                or _safe_get(checkpoint.get("metadata", {}), "parent_checkpoint_id")
            )
            results.append(
                CheckpointSummary(
                    checkpoint_id=cp_id,
                    thread_id=thread_id,
                    book_id=bid,
                    ts=ts,
                    metadata=meta,
                    parent_checkpoint_id=parent_cp_id,
                )
            )

        # 新→旧
        results.sort(key=lambda r: r.ts or "", reverse=True)
        return results[:limit] if (isinstance(limit, int) and limit > 0) else results

    # ===================== 删除整本书（按 book_id 聚合的所有数据） =====================

    async def delete_book(
        self,
        book_id: str,
        *,
        workspace_path: Path = Path("./workspace"),
        dry_run: bool = False,
    ) -> None:
        """
        删除与指定 book_id 相关的所有数据：
        - LangGraph Checkpoint：删除该书对应的所有线程（thread_id）的 checkpoint 记录
        - Writer Store：删除 ("writer_app_v2", book_id) 命名空间前缀下的所有键（包含 plan/settings/outline/scene/chapters 等及其向量分块）
        - Workspace 文件：删除 workspace 下任意书名目录中的 <book_id>/ 子目录
        """

        # 1) 汇总线程分支
        books = await self.list_books()
        target = next((b for b in books if b.book_id == book_id), None)
        threads = list(target.thread_ids) if target is not None else []

        # 2) 统计或删除 writer_app_v2/<book_id> 命名空间前缀下的所有键
        ns_prefix = ("writer_app_v2", book_id)
        store_total = 0
        scan_offset = 0
        while True:
            items = await self.store.asearch(ns_prefix, limit=1000, offset=scan_offset)
            if not items:
                break
            store_total += len(items)
            if not dry_run:
                for it in items:
                    try:
                        await self.store.adelete(it.namespace, it.key)
                    except Exception:
                        pass
            scan_offset += len(items)

        # 3) 统计或删除 workspace 下所有 */<book_id>/ 子目录
        ws_dirs: List[str] = []
        try:
            if workspace_path.exists():
                for book_name_dir in workspace_path.iterdir():
                    if not book_name_dir.is_dir():
                        continue
                    cand = book_name_dir / book_id
                    if cand.exists() and cand.is_dir():
                        ws_dirs.append(str(cand))
                        if not dry_run:
                            shutil.rmtree(cand, ignore_errors=True)
        except Exception:
            pass

        # 4) dry-run 输出或执行 checkpoint 删除
        if dry_run:
            print("\n[Dry-Run] 将要删除的资源：")
            print(f"- Checkpoint 线程数：{len(threads)} (按 thread_id 聚合)")
            print(f"- Store (writer_app_v2/{book_id}) 键总数：{store_total}")
            if ws_dirs:
                print(f"- 工作区目录：{len(ws_dirs)} 个")
                for p in ws_dirs:
                    print(f"  · {p}")
            else:
                print("- 工作区目录：0 个")
            return

        # 5) 非 dry-run：执行 checkpoint 删除
        if threads:
            for thread_id in threads:
                try:
                    await self.checkpointer.adelete_thread(thread_id)
                except Exception:
                    # 忽略个别分支清理失败，继续后续步骤
                    pass

    async def list_book_checkpoint_pairs(self, book_id: str) -> List[Dict[str, Any]]:
        """
        扁平化返回某本书的 checkpoint 列表（数据层 API，供 UI 直接消费）。
        返回的列表按时间从新到旧排序，字段与 list_checkpoints 一致。
        """
        cps = await self.list_checkpoints(book_id)
        return [
            {
                "checkpoint_id": c.checkpoint_id,
                "thread_id": c.thread_id,
                "ts": c.ts,
                "metadata": c.metadata,
                "parent_checkpoint_id": c.parent_checkpoint_id,
            }
            for c in cps
        ]

    # ===================== 构建历史树（按 book_id 聚合父子关系） =====================

    async def build_checkpoint_tree(
        self, book_id: str
    ) -> Tuple[List[str], Dict[str, Dict[str, Any]], Dict[str, List[str]]]:
        """
        构建指定 book_id 的 checkpoint 历史树。

        Returns:
            roots: 根 checkpoint_id 列表（无父或父缺失）
            nodes: {checkpoint_id: {id, parent, ts, thread_id, metadata}}
            children: {parent_id: [child_id, ...]}
        """
        nodes: Dict[str, Dict[str, Any]] = {}
        children: Dict[str, List[str]] = {}

        async for rec in self.checkpointer.alist({}):
            checkpoint = _safe_get(rec, "checkpoint", {}) or {}
            if not isinstance(checkpoint, dict):
                continue
            values = checkpoint.get("values") or checkpoint.get("state") or checkpoint
            # 解析 book_id（与 list_checkpoints 对齐）
            bid, _ = _extract_book_info(checkpoint, rec)
            if not bid:
                bid = _extract_thread_id(rec)
            if bid != book_id:
                continue

            # id / parent / thread
            cid = checkpoint.get("id") or _safe_get(rec, "checkpoint_id") or checkpoint.get("checkpoint_id")
            if not cid:
                continue
            pid = (
                _safe_get(rec, "parent_checkpoint_id")
                or _safe_get(checkpoint, "parent_checkpoint_id")
                or _safe_get(checkpoint.get("metadata", {}), "parent_checkpoint_id")
            )
            # 解析 thread_id，尽量与 list_checkpoints 的解析逻辑保持一致
            thread_id = _extract_thread_id(rec)
            ts = checkpoint.get("ts")

            # 元数据：采用 resume_info 的最新条目
            metadata: Dict[str, Any] = {}
            try:
                if isinstance(values, dict):
                    resume_list = values.get("resume_info")
                    if isinstance(resume_list, list) and resume_list:
                        last = resume_list[-1]
                        if isinstance(last, dict):
                            metadata = last
            except Exception:
                metadata = {}

            nodes[cid] = {
                "id": cid,
                "parent": pid,
                "ts": ts,
                "thread_id": thread_id,
                "metadata": metadata,
            }
            if pid:
                children.setdefault(pid, []).append(cid)

        # 根：无 parent 或者 parent 不存在于 nodes
        roots = [cid for cid, n in nodes.items() if not n.get("parent") or n.get("parent") not in nodes]
        return roots, nodes, children

    # ===================== 交互与格式化（统一输出/输入） =====================

    @staticmethod
    def format_book_line(index: int, book: BookGroupSummary) -> str:
        created = format_human_datetime(book.created_ts)
        updated = format_human_datetime(book.updated_ts)
        name = book.book_name or "(未命名)"
        return f"{index}. 【{name}】{book.book_id} 创建时间：{created}，更新时间：{updated}"

    @staticmethod
    def format_checkpoint_line(index: int, checkpoint: "CheckpointSummary") -> str:
        """
        以单行形式展示 checkpoint 概览信息：时间、人类可读摘要与少量关键信息。
        """
        ts_h = format_human_datetime(checkpoint.ts)
        meta = checkpoint.metadata or {}

        def _summary(meta_obj: Dict[str, Any]) -> str:
            if not isinstance(meta_obj, dict):
                return "(无摘要)"
            return meta_obj.get("summary") or (
                (meta_obj.get("node") or "")
                + (f"[{meta_obj.get('stage')}]" if meta_obj.get("stage") else "")
                + (f" -> {meta_obj.get('next')}" if meta_obj.get("next") else "")
            )

        def _meta_brief(meta_obj: Dict[str, Any]) -> str:
            if not isinstance(meta_obj, dict):
                return ""
            keys = [
                "node",
                "stage",
                "next",
                "type",
                "step_title",
                "step_index",
                "is_last_step",
                "target",
                "target_volume",
                "current_chapter",
                "chapter_index",
                "next_chapter",
                "buffer_len",
            ]
            pairs = []
            for k in keys:
                v = meta_obj.get(k)
                if v is not None and v != "":
                    pairs.append(f"{k}={v}")
            return (" | " + ", ".join(pairs)) if pairs else ""

        return (
            f"{index}. {checkpoint.checkpoint_id} @ {ts_h} (thread: {checkpoint.thread_id}) | "
            f"{_summary(meta)}{_meta_brief(meta)}"
        )

    # （移除）prompt_select_book：外部 CLI 已自定义菜单

    # （移除）prompt_select_checkpoint：改用树形选择

    # （移除）build_resume_config：外部直接构建 config


# ===================== 选择结果类型与选择逻辑（移除到 apps 层） =====================


class CheckpointManager(CheckpointManager):
    async def get_latest_state(self, book_id: str) -> Optional[Dict[str, Any]]:
        """
        获取指定book_id的最新状态。
        """
        try:
            # 获取该book_id的所有checkpoints
            checkpoints = await self.list_checkpoints(book_id)
            if not checkpoints:
                return None

            # 获取最新的checkpoint
            latest_checkpoint = checkpoints[0]  # list_checkpoints已经按时间倒序排列

            # 通过checkpointer获取完整的checkpoint数据
            config = {
                "configurable": {
                    "thread_id": latest_checkpoint.thread_id,
                    "checkpoint_id": latest_checkpoint.checkpoint_id,
                }
            }

            checkpoint_data = await self.checkpointer.aget(config)
            if checkpoint_data is None:
                return None

            # 提取channel_values作为状态
            if hasattr(checkpoint_data, "checkpoint"):
                checkpoint = checkpoint_data.checkpoint
            else:
                checkpoint = checkpoint_data

            if isinstance(checkpoint, dict):
                return checkpoint.get("channel_values", {})

            return None
        except Exception as e:
            print(f"获取最新状态失败: {e}")
            return None

    async def get_latest_checkpoint(self, book_id: str) -> Optional[CheckpointSummary]:
        """
        返回指定 book_id 对应的最新 CheckpointSummary（按时间从新到旧排序后的第一条）。
        若不存在则返回 None。
        """
        try:
            cps = await self.list_checkpoints(book_id, limit=1)
            return cps[0] if cps else None
        except Exception:
            return None

    async def get_latest_checkpoint_global(self) -> Optional[CheckpointSummary]:
        """
        全局范围内返回最新的 CheckpointSummary（不筛选 book_id）。
        """
        best: Optional[CheckpointSummary] = None
        best_ts: Optional[str] = None
        try:
            async for rec in self.checkpointer.alist({}):
                checkpoint = _safe_get(rec, "checkpoint", {}) or {}
                if not isinstance(checkpoint, dict):
                    continue
                # id 与 ts
                cp_id = checkpoint.get("id") or _safe_get(rec, "checkpoint_id") or checkpoint.get("checkpoint_id")
                if not cp_id:
                    continue
                ts = checkpoint.get("ts")
                # 解析 thread 与 book
                thread_id = _extract_thread_id(rec) or ""
                bid, _ = _extract_book_info(checkpoint, rec)
                if not bid:
                    bid = thread_id
                # 元数据（取 resume_info 最新一条）
                values = checkpoint.get("values") or checkpoint.get("state") or checkpoint
                meta: Dict[str, Any] = {}
                try:
                    if isinstance(values, dict):
                        resume_list = values.get("resume_info")
                        if isinstance(resume_list, list) and resume_list:
                            last = resume_list[-1]
                            if isinstance(last, dict):
                                meta = last
                except Exception:
                    meta = {}
                # 比较更新时间
                if best_ts is None or (ts or "") > best_ts:
                    best_ts = ts or ""
                    best = CheckpointSummary(
                        checkpoint_id=cp_id,
                        thread_id=thread_id,
                        book_id=bid or thread_id,
                        ts=ts,
                        metadata=meta,
                        parent_checkpoint_id=(
                            _safe_get(rec, "parent_checkpoint_id")
                            or _safe_get(checkpoint, "parent_checkpoint_id")
                            or _safe_get(checkpoint.get("metadata", {}), "parent_checkpoint_id")
                        ),
                    )
        except Exception:
            return best
        return best
