from __future__ import annotations

from dataclasses import dataclass, field
from typing import Dict, Optional


@dataclass
class RuntimeContext:
    """
    LangGraph 运行期上下文（不持久化）。
    - 仅在一次 graph 执行期间有效；下一次执行需重新传入
    - 用于承载不需要 checkpoint 的临时依赖/句柄（client/工具注册表/日志指标等）

    字段说明：
    - skip_policies: 跳过策略配置，格式为 {domain: count}
    - feedback_context: 当前feedback处理的上下文信息
    """

    # 跳过策略：{domain: remaining_count}
    skip_policies: Dict[str, int] = field(default_factory=dict)

    # feedback处理上下文（可选）
    feedback_context: Optional[Dict[str, any]] = field(default_factory=dict)
