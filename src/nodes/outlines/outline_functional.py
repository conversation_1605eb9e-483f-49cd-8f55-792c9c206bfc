from typing import Literal, Optional

from langchain_core.messages import BaseMessage, HumanMessage
from langgraph.config import RunnableConfig
from langgraph.types import Command

from src.context.context_builder import ContextBuilder
from src.nodes.common.book_types import (
    BookDetail,
    BookVolumeOutlinesSchema,
    CharacterDetailCollection,
    VolumeOutline,
    WriterPlan,
)
from src.nodes.common.node_kit import build_resume_info, llm_json, maybe_interrupt_review, write_text_under_book
from src.nodes.common.store_manager import WriterStoreManager
from src.nodes.outlines.outline_prompt import bo_init_prompt_template
from src.nodes.outlines.outline_tasks import outline_refine_task
from src.state import State


def _get_current_chapter_number(book_detail: Optional[BookDetail]) -> int:
    if book_detail is None or not getattr(book_detail, "chapters", None):
        return 1
    return len(book_detail.chapters) + 1


def _build_init_messages(state: State, config: RunnableConfig) -> list[BaseMessage]:
    writer_current_plan: WriterPlan = state.get("writer_current_plan")
    character_details: CharacterDetailCollection = state.get("character_details")

    character_content = character_details.get_characters_content() if character_details else ""
    volum_outlines_prompt = bo_init_prompt_template.format(
        book_name=writer_current_plan.book_name,
        book_description=writer_current_plan.book_description,
        refined_book_setting=state.get("refined_book_setting"),
        character_design=character_content,
    )

    cb = ContextBuilder()
    cb.header()
    cb.section("大纲任务", volum_outlines_prompt)
    cb.json_schema(BookVolumeOutlinesSchema.model_json_schema())

    demo_chapter = config.get("configurable", {}).get("demo_chapter")
    cb.demo(demo_chapter)

    budget = config.get("configurable", {}).get("prompt_budget_tokens")
    return cb.build(max_tokens=budget, policy="outline")


def _build_next_volume_messages(state: State, config: RunnableConfig, next_volume_number: int) -> list[BaseMessage]:
    character_details: CharacterDetailCollection = state.get("character_details")
    character_content = character_details.get_characters_content() if character_details else ""

    # 汇总已存在卷大纲文本作为参考
    book_detail: BookDetail | None = state.get("book_detail")
    existing_outlines = book_detail.get_completed_volume_outlines() if book_detail else ""

    prompt_text = (
        f"请基于设定与已完成卷纲，为第{next_volume_number}卷生成卷纲。\n"
        f"要求：符合爽文风格，提供卷标题、核心矛盾、推进目标与关键事件串。仅输出 JSON 符合 VolumeOutline。"
    )

    cb = ContextBuilder()
    cb.header()
    cb.section("设定集", state.get("refined_book_setting"))
    cb.section("角色设计", character_content)
    if existing_outlines:
        cb.section("已完成卷纲参考", existing_outlines)
    cb.section("生成任务", prompt_text)
    cb.json_schema(VolumeOutline.model_json_schema())

    budget = config.get("configurable", {}).get("prompt_budget_tokens")
    return cb.build(max_tokens=budget, policy="outline")


async def writer_outline_fn(
    state: State, config: RunnableConfig
) -> Command[Literal["writer_scene", "writer_outline", "writer_character", "bs_planner", "bs_steps", "bs_refine"]]:
    plan: WriterPlan | None = state.get("writer_current_plan")
    book_id = state.get("book_id")
    book_name = state.get("book_name")
    character_summaries = state.get("character_summaries")
    character_details: CharacterDetailCollection | None = state.get("character_details")
    book_detail: BookDetail | None = state.get("book_detail")

    # 前置检查：对齐旧实现
    if not plan or not plan.is_all_step_completed():
        return Command(goto="bs_steps")
    if not plan.is_all_step_refined():
        return Command(goto="bs_refine")
    if book_id is None:
        return Command(goto="bs_planner")
    if character_summaries is None or not getattr(character_summaries, "characters", None):
        return Command(goto="writer_character")
    if character_details is None:
        return Command(goto="writer_character")
    # 若 summaries 与 details 数量不一致：记录到 resume_info，继续流程（不自恢复，不阻断）
    if character_details and getattr(character_details, "characters", None):
        expected = [s.name for s in (character_summaries.characters or [])]
        have = [ch.name for ch in (character_details.characters or [])]
        if len(have) != len(expected):
            # 仅记录诊断信息，允许后续继续；由角色节点在后续 tick 中补齐
            diag = {
                "node": "writer_outline",
                "summary": "角色详情数量与简介数量不一致，已继续",
                "expected_count": len(expected),
                "have_count": len(have),
                "expected_samples": expected[:4],
                "have_samples": have[:4],
            }
            # 由于本函数可能多次进入，这里采用增量写入
            state_resume = state.get("resume_info") or []
            state_resume.append(diag)
            # 更新本地变量用于后续判断，不抛错

    # 若外部指明需要生成下一卷（增量），优先处理
    try:
        desired_next_vol = state.get("outline_generate_next_volume_number")
    except Exception:
        desired_next_vol = None

    if desired_next_vol is not None:
        next_vol_num = int(desired_next_vol)
        messages = _build_next_volume_messages(state, config, next_vol_num)
        parsed: VolumeOutline = await llm_json("writer_outline:next_volume", messages, VolumeOutline, config=config)
        if not parsed or not parsed.volume_title:
            raise AssertionError("❌ 生成下一卷失败：结果为空或缺少标题")

        # 追加到现有 book_detail
        if book_detail is None:
            book_detail = BookDetail(volume_outlines=[parsed])
        else:
            book_detail.volume_outlines.append(parsed)

        # 落盘与入库
        file_rel = f"outline/第{parsed.volume_number}卷-大纲.md"
        review_file = write_text_under_book(book_name, book_id, file_rel, parsed.get_completed_contents())
        try:
            if book_id:
                sm = WriterStoreManager(book_id=book_id)
                await sm.store_volume_outline(book_detail)
        except RuntimeError as e:
            if "Called get_config outside of a runnable context" not in str(e):
                raise

        update = {"book_detail": book_detail, "outline_generate_next_volume_number": None}
        update["resume_info"] = build_resume_info(
            state,
            node="writer_outline",
            next_node="writer_scene",
            summary=f"大纲：增量生成第{parsed.volume_number}卷 -> writer_scene",
            volume_number=parsed.volume_number,
        )
        return Command(update=update, goto="writer_scene")

    # 阶段判定
    current_chapter = _get_current_chapter_number(book_detail)
    if book_detail is None or not book_detail.volume_outlines:
        stage = "init_volume_outlines"
    elif getattr(
        book_detail, "need_volume_outline_refinement_for_current_progress", None
    ) and book_detail.need_volume_outline_refinement_for_current_progress(current_chapter):
        stage = "refine_volume_outlines"
    else:
        stage = "completed"

    # 已完成 → 进入下游
    if stage == "completed":
        return Command(goto="writer_scene")

    # 初始化阶段
    if stage == "init_volume_outlines":
        messages = _build_init_messages(state, config)
        # 稳健性：异常→自动重试（一次）→ 人审
        try:
            parsed = await llm_json("writer_outline", messages, BookVolumeOutlinesSchema, config=config)
            if not getattr(parsed, "volume_outlines", None):
                raise ValueError("empty_volume_outlines")
        except Exception as e:
            # 一次回退重试：追加提示并可选升级 tier
            retry_messages = list(messages)
            retry_messages.append(
                HumanMessage(
                    name="review",
                    content="请严格输出有效 JSON，符合给定 Schema（不得包含注释/示例文字/占位），并确保非空的分卷大纲列表。",
                )
            )
            try:
                parsed = await llm_json(
                    "writer_outline",
                    retry_messages,
                    BookVolumeOutlinesSchema,
                    config=config,
                    stage_name="retry",
                    tier_override="high",
                )
                if not getattr(parsed, "volume_outlines", None):
                    raise ValueError("empty_volume_outlines_retry")
            except Exception as e2:
                # 仍失败：进入人审（写入占位草稿，展示错误摘要）
                book_id = state.get("book_id")
                book_name = state.get("book_name")
                error_text = f"大纲初始化失败：{str(e2) or str(e)}\n\n请在此文件中直接提供分卷大纲草案或意见，或修改后输入 /pass 继续。"
                review_file = write_text_under_book(book_name, book_id, "outline/整书分卷大纲-待审.md", error_text)

                update = {}
                hr_update, next_node = await maybe_interrupt_review(
                    state=state,
                    config=config,
                    node_name="writer_outline",
                    input_messages=messages,
                    review_file_name=review_file,
                    review_content=error_text,
                    next_node_if_pass="writer_outline",
                )
                if hr_update is None:
                    hr_update, next_node = {}, "writer_outline"
                update.update(hr_update)
                update["resume_info"] = build_resume_info(
                    state,
                    node="writer_outline",
                    next_node=next_node,
                    summary=f"大纲：初始化失败进入人审 -> {next_node}",
                )
                return Command(update=update, goto=next_node)

        book_detail_new = BookDetail(volume_outlines=parsed.volume_outlines)

        # 落盘
        file_rel = "outline/整书分卷大纲.md"
        review_file = write_text_under_book(
            book_name, book_id, file_rel, book_detail_new.get_completed_volume_outlines()
        )

        # 入库
        try:
            if book_id:
                sm = WriterStoreManager(book_id=book_id)
                await sm.store_volume_outline(book_detail_new)
        except RuntimeError as e:
            if "Called get_config outside of a runnable context" not in str(e):
                raise

        update = {"book_detail": book_detail_new}

        # interrupt 审核
        hr_update, next_node = await maybe_interrupt_review(
            state=state,
            config=config,
            node_name="writer_outline",
            input_messages=messages,
            review_file_name=review_file,
            review_content=book_detail_new.get_completed_volume_outlines(),
            next_node_if_pass="writer_outline",
        )
        if hr_update is None:
            hr_update, next_node = {}, "writer_outline"
        update.update(hr_update)

        update["resume_info"] = build_resume_info(
            state,
            node="writer_outline",
            next_node=next_node,
            summary=f"大纲：初始化分卷 -> {next_node}",
            start_vol=1,
            end_vol=len(book_detail_new.volume_outlines or []),
        )
        return Command(update=update, goto=next_node)

    # 细化阶段（并发）：基于当前章节进度细化当前卷到后续2卷内所有 version==0 的卷
    # 调试期：断言 book_detail/volume_outlines 存在
    assert book_detail is not None and getattr(
        book_detail, "volume_outlines", None
    ), "[ASSERT] writer_outline_fn: 进入细化阶段但 book_detail 缺失或无 volume_outlines"

    current_volume_number = (current_chapter - 1) // 100 + 1
    max_required_volume = current_volume_number + 2

    target_indices: list[int] = []
    for i, vol in enumerate(book_detail.volume_outlines):
        if vol.version == 0 and current_volume_number <= vol.volume_number <= max_required_volume:
            target_indices.append(i)

    if not target_indices:
        return Command(goto="writer_scene")

    # 节点内用 @task 并发细化并直接聚合
    character_details: CharacterDetailCollection | None = state.get("character_details")
    character_content = character_details.get_characters_content() if character_details else ""
    refined_book_outlines_text = book_detail.get_completed_volume_outlines()

    cfg = {}
    try:
        cfg = config.get("configurable", {})
    except Exception:
        try:
            cfg = getattr(config, "configurable", {}) or {}
        except Exception:
            cfg = {}

    futures = []
    for i in target_indices:
        vol = book_detail.volume_outlines[i]
        payload = {
            "refined_book_setting": state.get("refined_book_setting"),
            "character_content": character_content,
            "refined_book_outlines": refined_book_outlines_text,
            "volume_index": i,
            "volume_number": vol.volume_number,
            "volume_title": vol.volume_title,
            "version": getattr(vol, "version", 0),
            "book_name": book_name,
            "book_id": book_id,
            "configurable": cfg,
        }
        futures.append(outline_refine_task(payload))

    # 收集结果（优先 .result()；失败则 await 以确保完成）
    results = []
    for f in futures:
        try:
            results.append(f.result())
        except Exception:
            try:
                results.append(await f)
            except Exception as e:
                results.append({"error": str(e)})

    # 应用结果回写到 book_detail
    for r in results:
        try:
            idx = int(r.get("index"))
            refined_dump = r.get("refined")
            if not isinstance(refined_dump, dict):
                continue
            refined = VolumeOutline.model_validate(refined_dump)
            book_detail.volume_outlines[idx] = refined
        except Exception:
            continue

    # 批量文件
    lines = [
        "# 卷细化批量更新",
        "",
        f"范围：第{current_volume_number}~{max_required_volume}卷",
        "",
        "---",
        "",
    ]
    for i in sorted(target_indices, key=lambda j: book_detail.volume_outlines[j].volume_number):
        refined = book_detail.volume_outlines[i]
        lines.append(f"## 第{refined.volume_number}卷 {refined.volume_title}")
        lines.append("")
        lines.append(refined.get_completed_contents())
        lines.append("")
    batch_content = "\n".join(lines)
    batch_file_rel = f"outline/批量-第{current_volume_number}~{max_required_volume}卷-细化大纲.md"
    review_file = write_text_under_book(book_name, book_id, batch_file_rel, batch_content)

    # 入库（一次）
    try:
        if book_id:
            sm = WriterStoreManager(book_id=book_id)
            await sm.store_volume_outline(book_detail)
    except RuntimeError as e:
        if "Called get_config outside of a runnable context" not in str(e):
            raise

    update = {"book_detail": book_detail, "outline_last_volume_number": max_required_volume}

    # 审核（一次）
    hr_update, next_node = await maybe_interrupt_review(
        state=state,
        config=config,
        node_name="writer_outline",
        input_messages=[],
        review_file_name=review_file,
        review_content=batch_content,
        next_node_if_pass="writer_scene",
    )
    if hr_update:
        update.update(hr_update)
    if not next_node:
        next_node = "writer_scene"

    update["resume_info"] = build_resume_info(
        state,
        node="writer_outline",
        next_node=next_node,
        summary=f"大纲：并发细化完成 -> {next_node}",
        expected_indices=target_indices,
        current_volume=current_volume_number,
        max_required_volume=max_required_volume,
        outlines_count=len(book_detail.volume_outlines or []),
    )

    return Command(update=update, goto=next_node)


__all__ = ["writer_outline_fn"]
