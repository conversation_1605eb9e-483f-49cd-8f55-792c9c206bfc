from __future__ import annotations

from langgraph.func import task

from src.context.context_builder import ContextBuilder
from src.nodes.common.book_types import VolumeOutline
from src.nodes.outlines.outline_prompt import bo_detail_prompt_template


@task
async def outline_refine_task(payload: dict) -> dict:
    """
    LangGraph task：细化单卷大纲并落盘（副作用仅执行一次）。
    payload:
      - refined_book_setting: str
      - character_content: str
      - refined_book_outlines: str
      - volume_index: int
      - volume_number: int
      - volume_title: str
      - book_name: str
      - book_id: str
      - configurable: dict
    return: {"index": int, "refined": dict, "file_rel": str}
    """
    from src.nodes.common.node_kit import llm_json, write_text_under_book  # 延迟导入避免循环

    refined_book_setting = payload.get("refined_book_setting")
    character_content = payload.get("character_content", "")
    refined_book_outlines = payload.get("refined_book_outlines", "")
    idx = int(payload.get("volume_index"))
    vol_number = int(payload.get("volume_number"))
    vol_title = payload.get("volume_title", "")

    prompt_text = bo_detail_prompt_template.format(
        refined_book_setting=refined_book_setting,
        character_design=character_content,
        refined_book_outlines=refined_book_outlines,
        volume_index=vol_number,
    )
    cb = ContextBuilder()
    cb.header()
    cb.section("大纲细化任务", prompt_text)
    cb.json_schema(VolumeOutline.model_json_schema())
    messages = cb.build(max_tokens=None, policy="outline")

    config = {"configurable": payload.get("configurable", {})}
    refined_one: VolumeOutline = await llm_json("writer_outline", messages, VolumeOutline, config=config)
    refined_one.version = (payload.get("version", 0) or 0) + 1
    refined_one.volume_number = vol_number
    refined_one.volume_title = vol_title

    # 落盘单卷
    book_name = payload.get("book_name", "")
    book_id = payload.get("book_id", "")
    file_rel_one = f"outline/第{refined_one.volume_number}卷-细化大纲-v{refined_one.version}.md"
    try:
        _ = write_text_under_book(book_name, book_id, file_rel_one, refined_one.get_completed_contents())
    except Exception:
        pass

    return {"index": idx, "refined": refined_one.model_dump(), "file_rel": file_rel_one}


__all__ = [
    "outline_refine_task",
]
