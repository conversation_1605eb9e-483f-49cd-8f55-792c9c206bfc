from langchain_core.prompts import PromptTemplate

bo_init_prompt_template = PromptTemplate(
    input_variables=[
        "book_name",
        "book_description",
        "refined_book_setting",
        "character_design",
    ],
    template=r"""

你是一位专业的玄幻爽文网络小说策划师，现在需要基于已完成的设定集为小说《{book_name}》创建详细的分卷大纲。

**书籍信息**:
- 书名: {book_name}
- 简介: {book_description}

**已完成的小说设定集**:
{refined_book_setting}

**角色设计**:
{character_design}

**重要约束**:
- **只能使用以上角色设计中已明确设定的角色**
- **严禁创造或引入角色设计中没有的新角色**
- **龙套角色、路人甲乙、临时出现的小角色等均不应出现在大纲中**
- **大纲应专注于已设定角色的成长、互动和冲突**
- **每卷大纲必须明确列出本卷的主要出场角色（从已设定角色中选择）**

**大纲创建要求**:
- 保持与设定集的一致性
- 整书千万字以上，至少分10卷，每卷按100章体量设计
- 每卷需要有明确的主题和故事线、核心冲突、核心地图、关键成长、阶段高潮、核心伏笔、核心人物关系发展等
- 确保整书故事节奏合理，高潮迭起，松弛有度
- 前10章展开要迅速，节奏强，吸引读者留存
- 整书风格：保持与设定集的一致性，爽文发展，扮猪吃虎，不要出现主角被虐的情况
- 循序渐进，不要过快推动剧情，要给读者留足思考时间

**注意，要符合爽文风格，最终卷的结局留有一点点的悬念，以便续作或同世界观新作的展开**

""",
)

bo_demo_chapter_template = PromptTemplate(
    template="""
--------------------------------
已初步定稿的前几章如下，大纲规划时需要参考
<demo>{demo_chapter}</demo>
--------------------------------
""",
    input_variables=["demo_chapter"],
)

bo_detail_prompt_template = PromptTemplate(
    input_variables=[
        "refined_book_setting",
        "character_design",
        "refined_book_outlines",
        "volume_index",
    ],
    template="""
你是专业的玄幻爽文网络小说创作助手，我们正在细化章节大纲。

以下是本书设定集：
<设定集>
{refined_book_setting}
</设定集>

以下是角色设计：
<角色设计>
{character_design}
</角色设计>

以下是现有大纲，还比较粗略：

<现有大纲>
{refined_book_outlines}
</现有大纲>

**重要约束**:
- **只能使用以上角色设计中已明确设定的角色**
- **严禁创造或引入角色设计中没有的新角色**
- **龙套角色、路人甲乙、临时出现的小角色等均不应出现在大纲中**
- **大纲应专注于已设定角色的成长、互动和冲突**
- **必须明确列出本卷的主要出场角色（从已设定角色中选择）**

现在需要对第 {volume_index} 卷的大纲按起承转合的设计方式进行细化，输出5000字的完整卷大纲：
- 起（目标+障碍）
- 承（主角和其他角色之间行动+结果拉扯，情绪对立的拉扯）
- 转（1-3个反转情节+1-3个意外，情绪高潮，情节的转折点）
- 合（分卷结尾，情节走向的重要转折点）

**注意，要符合爽文风格**
""",
)

__all__ = ["bo_init_prompt_template", "bo_demo_chapter_template", "bo_detail_prompt_template"]
