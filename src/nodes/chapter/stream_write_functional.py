from typing import Literal

from langgraph.config import RunnableConfig
from langgraph.types import Command

from src.context.context_builder import Con<PERSON><PERSON><PERSON><PERSON>
from src.context.policies import STREAM_WRITING_GUIDANCE
from src.nodes.common.book_types import BookDetail
from src.nodes.common.node_kit import build_stream_resume_info, llm_text, maybe_interrupt_review, write_text_under_book
from src.state import State


def _build_stream_messages(state: State, config: RunnableConfig):
    book_detail: BookDetail = state.get("book_detail")
    refined_book_setting = state.get("refined_book_setting")
    book_name = state.get("book_name") or state.get("writer_current_plan").book_name

    recent_buffer = book_detail.stream_buffer[-1000:] if book_detail and book_detail.stream_buffer else ""

    current_idx = (len(book_detail.chapters) + 1) if book_detail else 1
    vo = book_detail.get_volume_outline_for_chapter(current_idx) if book_detail else None
    volume_outline = vo.get_completed_contents() if vo else "（当前卷大纲未就绪）"

    volume_core_summary = ""
    if vo:
        theme_text = vo.volume_title or f"第{vo.volume_number}卷"
        conflict_summary = "主角与强敌/环境/规则的对抗"
        scene_design = state.get("scene_design", None)
        if scene_design and hasattr(scene_design, "get_volume_scenes"):
            try:
                volume_scenes = scene_design.get_volume_scenes(vo.volume_number)
                if volume_scenes and volume_scenes.scenes:
                    all_conflicts = []
                    for sc in volume_scenes.scenes:
                        if getattr(sc, "conflicts", None):
                            all_conflicts.extend([c for c in sc.conflicts if c and c.strip()])
                    seen = set()
                    dedup = []
                    for c in all_conflicts:
                        if c not in seen:
                            seen.add(c)
                            dedup.append(c)
                    if dedup:
                        conflict_summary = ", ".join(dedup[:2])
            except Exception:
                pass
        goal_text = f"完成第{vo.volume_number}卷阶段目标，推进主线与角色成长"
        volume_core_summary = "\n".join(
            [f"- 主题：{theme_text}", f"- 核心冲突：{conflict_summary}", f"- 目标：{goal_text}"]
        )

    scene_info = ""
    related_characters = ""
    recent_summaries = []
    scene_design = state.get("scene_design", None)
    character_details = state.get("character_details", None)
    if scene_design and book_detail:
        scene = scene_design.get_scene_for_chapter(current_idx)
        if scene:
            scene_info = scene.get_scene_content()
            if character_details and scene.featured_characters:
                infos = []
                for name in scene.featured_characters:
                    char = character_details.get_character_by_name(name)
                    if char:
                        infos.append(char.get_full_content())
                related_characters = "\n\n".join(infos)
        for back in range(1, 4):
            prev_scene = scene_design.get_scene_for_chapter(current_idx - back)
            if prev_scene:
                recent_summaries.append(prev_scene.get_scene_content())
    if recent_summaries:
        scene_info = ("\n\n".join(recent_summaries) + "\n\n" + scene_info).strip()

    unresolved_short = ""
    if book_detail and book_detail.chapters:
        try:
            last_chapter = book_detail.chapters[-1]
            if getattr(last_chapter, "think_todo_outline", None):
                lines = [ln.strip() for ln in str(last_chapter.think_todo_outline).splitlines() if ln.strip()]
                if lines:
                    unresolved_short = "\n".join([f"- {ln}" for ln in lines[:5]])
        except Exception:
            unresolved_short = ""

    target_append_words = 8000

    cb = ContextBuilder()
    cb.header()

    # 根据写作状态调整提示词与上下文
    writing_state = (state.get("writing_state") or "IN_SCENE").upper()
    if writing_state not in ("IN_SCENE", "SWITCHING_SCENE"):
        writing_state = "IN_SCENE"

    if writing_state == "SWITCHING_SCENE":
        write_req = (
            "- 本次以‘场景过渡’为目标，优先完成从当前场景到下一场景的自然过渡\n"
            "- 衔接合理、节奏利落，避免复述；必要时用1-3段引出下一场景开端\n"
            "- 只生成正文，不要标题、不要分章、不要总结\n"
            "- 不要凭空引入未设定的角色（仅使用角色设定中已有角色）\n"
            "- 保持爽点密度、推进矛盾冲突、适时补充世界观信息"
        )
    else:
        write_req = (
            f"- 续写不低于 {target_append_words} 字，保持内容连贯、信息密度高、节奏鲜明\n"
            "- 只生成正文，不要标题、不要分章、不要总结\n"
            "- 不要凭空引入未设定的角色（仅使用角色设定中已有角色）\n"
            "- 保持爽点密度、推进矛盾冲突、适时补充世界观信息"
        )

    cb.section(
        "写作模式", f"你是一位专业的玄幻爽文网络小说作家。我们采用“流式写作+智能分章”模式：\n{STREAM_WRITING_GUIDANCE}"
    )
    cb.section("写作要求", write_req)
    cb.section("书籍信息", book_name)
    cb.section("设定集", refined_book_setting)
    if volume_core_summary:
        cb.section("卷级核心摘要（短）", volume_core_summary)
    cb.section("当前卷大纲（摘要）", volume_outline)
    cb.section("当前场景信息", scene_info or "本次续写无需限定场景，可自然推进")

    # 注入多层次摘要，帮助模型把握上下文
    try:
        book_summary = state.get("book_summary")
        if book_summary:
            recent_l1 = book_summary.get_recent_l1(5)
            l1_text = "\n".join([f"第{n}章：{s}" for n, s in recent_l1]) if recent_l1 else ""
            l2_latest = book_summary.get_latest_l2()
            l3_latest = book_summary.get_latest_l3()
            if l1_text:
                cb.section("最近章节摘要（L1）", l1_text)
            if l2_latest:
                cb.section("最近10章摘要（L2）", f"块{l2_latest[0]}：{l2_latest[1]}")
            if l3_latest:
                cb.section("最近50章摘要（L3）", f"块{l3_latest[0]}：{l3_latest[1]}")
    except Exception:
        pass

    # 若为过渡状态，提供下一场景提示
    if writing_state == "SWITCHING_SCENE":
        next_scene_info = ""
        try:
            if scene_design and book_detail:
                next_scene = scene_design.get_scene_for_chapter(current_idx)
                if next_scene:
                    next_scene_info = next_scene.get_scene_content()
        except Exception:
            next_scene_info = ""
        if next_scene_info:
            cb.section("下一场景提示", next_scene_info)

    if unresolved_short:
        cb.section("未解悬念清单（短）", unresolved_short)
    cb.section("相关角色设定", related_characters or "本续写无需特定出场角色")
    cb.section("最近缓冲区内容（避免重复衔接，仅参考，不要复述）", recent_buffer)

    budget = config.get("configurable", {}).get("prompt_budget_tokens")
    return cb.build(max_tokens=budget, policy="stream")


async def stream_write_fn(
    state: State, config: RunnableConfig
) -> Command[Literal["stream_write", "chapter_segment", "__end__"]]:
    plan = state.get("writer_current_plan")
    if not plan or not plan.is_all_step_completed():
        raise AssertionError("❌ 策划案未完成，无法进行流式写作")
    if state.get("book_id") is None:
        raise AssertionError("book_id 为空")
    if state.get("book_detail") is None:
        raise AssertionError("❌ 大纲未生成，无法进行流式写作")

    messages = _build_stream_messages(state, config)

    content, _ = await llm_text("stream_write", messages, config=config)

    book_detail: BookDetail = state.get("book_detail")
    if not book_detail.stream_buffer:
        book_detail.stream_buffer = content
    else:
        book_detail.stream_buffer += "\n\n" + content

    review_file = write_text_under_book(
        state.get("book_name"), state.get("book_id"), "chapters_stream/stream_buffer.md", book_detail.stream_buffer
    )

    # 达到阈值 → 注入人审 / 或强制切换以避免无限自循环
    max_buffer = 8000
    try:
        cfg = config.get("configurable") or {}
        base = int(cfg.get("chapter_cnt_base") or 0)
        add_n = cfg.get("write_chapters_cnt")
        final_target = None if add_n is None else int(base) + int(add_n)
        if final_target is not None and len(book_detail.chapters) >= final_target:
            return Command(update={"book_detail": book_detail}, goto="__end__")
    except Exception:
        pass

    # 自循环保险丝：当连续 self-loop 次数超过上限时，强制进入分章
    try:
        max_self_loops = int((config.get("configurable", {}) or {}).get("stream_write_max_self_loops", 8))
    except Exception:
        max_self_loops = 8
    try:
        self_loops_now = int(state.get("stream_write_self_loops") or 0) + 1
    except Exception:
        self_loops_now = 1

    if len(book_detail.stream_buffer) >= max_buffer or self_loops_now >= max_self_loops:
        # 进入分章前的人审（若启用）；否则直接分章
        hr_update, next_node = await maybe_interrupt_review(
            state=state,
            config=config,
            node_name="stream_write",
            input_messages=messages,
            review_file_name=review_file,
            review_content=book_detail.stream_buffer,
            next_node_if_pass="chapter_segment",
        )
        if hr_update is None:
            hr_update, next_node = {}, "chapter_segment"

        # 当进入分章前，派发流式后处理任务（按固定分块大小切分当前缓冲快照）
        # 仅当缓冲达到阈值时才派发；若是自循环触发，则跳过后处理以减少开销
        do_post = len(book_detail.stream_buffer) >= max_buffer
        update = {"book_detail": book_detail, "stream_write_self_loops": 0}
        update.update(hr_update)

        if do_post:
            chunk_size = int((config.get("configurable", {}) or {}).get("stream_post_chunk_size", 2000))
            buffer_snapshot = str(book_detail.stream_buffer or "")
            chunks: list[str] = []
            if chunk_size > 0 and len(buffer_snapshot) > 0:
                for i in range(0, len(buffer_snapshot), chunk_size):
                    chunks.append(buffer_snapshot[i : i + chunk_size])
            indices = list(range(len(chunks)))
            batch_id = str(len(book_detail.chapters) + 1)
            if indices:
                update.update(
                    {
                        "stream_post_batch_id": batch_id,
                        "stream_post_chunks": chunks,
                        "stream_post_expected_indices": indices,
                        "stream_post_pending_indices": [],
                        "stream_post_dispatched_indices": [],
                        "resume_info": build_stream_resume_info(
                            state,
                            node="stream_write",
                            next_node="chapter_segment",
                            buffer_len=len(book_detail.stream_buffer),
                            chunks_count=len(indices),
                        ),
                    }
                )
                # 节点内并发处理分块
                from src.nodes.chapter.stream_write_tasks import stream_post_task

                futures = []
                for i, chunk_text in enumerate(chunks):
                    payload = {
                        "batch_id": batch_id,
                        "chunk_index": i,
                        "chunk_text": chunk_text,
                        "book_name": state.get("book_name") or state.get("writer_current_plan").book_name,
                        "book_id": state.get("book_id"),
                        "configurable": (config.get("configurable") or {}),
                    }
                    futures.append(stream_post_task(payload))
                # 收集结果（优先 .result()；失败则 await）
                results = []
                for f in futures:
                    try:
                        results.append(f.result())
                    except Exception:
                        try:
                            results.append(await f)
                        except Exception as e:
                            results.append({"error": str(e)})

                # 写入批量索引文件
                lines = [
                    "# 流式后处理批量结果",
                    "",
                    f"批次: {batch_id}",
                    "",
                    "---",
                    "",
                ]
                for item in sorted(results, key=lambda x: int(x.get("index", 0))):
                    idx = int(item.get("index", 0))
                    rel_file = item.get("file") or f"chapters_stream/batch-{batch_id}/chunk-{idx + 1:03d}.md"
                    doc_key = item.get("doc_key") or "-"
                    lines.append(f"- 分块 {idx + 1:03d}: 文件 {rel_file} | 文档键 {doc_key}")
                summary_rel = f"chapters_stream/batch-{batch_id}/批量-流式后处理.md"
                _ = write_text_under_book(
                    state.get("book_name") or state.get("writer_current_plan").book_name,
                    state.get("book_id"),
                    summary_rel,
                    "\n".join(lines),
                )

                return Command(update=update, goto="chapter_segment")

        # 无需后处理，直接进入下一节点
        update["resume_info"] = build_stream_resume_info(
            state,
            node="stream_write",
            next_node=next_node,
            buffer_len=len(book_detail.stream_buffer),
        )
        return Command(update=update, goto=next_node)

    # 继续流式写作（自循环计数+1）
    update = {
        "book_detail": book_detail,
        "stream_write_self_loops": self_loops_now,
        "resume_info": build_stream_resume_info(
            state,
            node="stream_write",
            next_node="stream_write",
            buffer_len=len(book_detail.stream_buffer),
        ),
    }
    return Command(update=update, goto="stream_write")
