from typing import Literal

from langgraph.config import RunnableConfig
from langgraph.types import Command

from src.context.context_builder import Context<PERSON>uilder
from src.nodes.chapter.stream_write_prompt import segment_prompt_template
from src.nodes.common.book_types import BookDetail, Chapter, SegmentedChapterResult
from src.nodes.common.node_kit import build_resume_info, llm_json, rel_path_for_chapter, write_text_under_book
from src.state import State


def _build_segment_messages(state: State, config: RunnableConfig) -> list:
    book_detail: BookDetail = state.get("book_detail")
    min_len = config.get("configurable", {}).get("segment_min_len", 3000)
    max_len = config.get("configurable", {}).get("segment_max_len", 5000)

    cb = ContextBuilder()
    cb.header()
    # 计算缓冲与长度
    buffer_text = book_detail.stream_buffer if book_detail and book_detail.stream_buffer else ""
    cb.section(
        "智能分章任务",
        segment_prompt_template.format(
            book_name=(
                state.get("writer_current_plan").book_name
                if state.get("writer_current_plan")
                else state.get("book_name") or ""
            ),
            buffer_text=buffer_text,
            min_len=min_len,
            max_len=max_len,
            len=len(buffer_text),
        ),
    )
    cb.json_schema(SegmentedChapterResult.model_json_schema())
    budget = config.get("configurable", {}).get("prompt_budget_tokens")
    return cb.build(max_tokens=budget, policy="chapter_segment")


async def chapter_segment_fn(
    state: State, config: RunnableConfig
) -> Command[Literal["stream_write", "chapter_segment", "__end__"]]:
    # 前置检查
    book_detail: BookDetail | None = state.get("book_detail")
    if not book_detail or not book_detail.stream_buffer:
        raise AssertionError("❌ 缓冲为空，无法分章")

    # 构建消息并请求 LLM（结构化）
    messages = _build_segment_messages(state, config)
    seg: SegmentedChapterResult = await llm_json("chapter_segment", messages, SegmentedChapterResult, config=config)

    # 写入章节编号并追加（仅处理正文，智能分章职责收敛）
    next_index = (len(book_detail.chapters) + 1) if book_detail and book_detail.chapters else 1
    ch = Chapter(
        chapter_number=next_index,
        chapter_title=seg.title,
        chapter_content=seg.chapter_content,
    )
    book_detail.chapters.append(ch)

    # 使用 LLM 返回的剩余文本回灌缓冲
    remaining = (seg.remaining_text or "").lstrip()
    book_detail.stream_buffer = remaining

    # 写入章节文件
    file_rel = rel_path_for_chapter(next_index, ch.chapter_title)
    write_text_under_book(state.get("book_name"), state.get("book_id"), file_rel, ch.get_completed_content())

    # 终止条件：达到目标章数（基于 chapter_cnt_base 追加 N 章）
    try:
        cfg = config.get("configurable") or {}
        base = int(cfg.get("chapter_cnt_base") or 0)
        add_n = cfg.get("write_chapters_cnt")
        final_target = None if add_n is None else int(base) + int(add_n)
        if final_target is not None and len(book_detail.chapters) >= final_target:
            update = {
                "book_detail": book_detail,
                "resume_info": build_resume_info(
                    state,
                    node="chapter_segment",
                    next_node="__end__",
                    summary=f"智能分章：生成第{next_index}章，缓冲{len(book_detail.stream_buffer)}字 -> 完成",
                ),
            }
            return Command(update=update, goto="__end__")
    except Exception:
        pass

    # 决定下一步：进入 prepare_next_chapter，由其决定是否切换场景/继续写作
    update = {
        "book_detail": book_detail,
        "resume_info": build_resume_info(
            state,
            node="chapter_segment",
            next_node="prepare_next_chapter",
            summary=f"智能分章：生成第{next_index}章，缓冲{len(book_detail.stream_buffer)}字 -> prepare_next_chapter",
            chapter_number=next_index,
            buffer_len=len(book_detail.stream_buffer),
        ),
    }
    return Command(update=update, goto="prepare_next_chapter")


__all__ = ["chapter_segment_fn"]
