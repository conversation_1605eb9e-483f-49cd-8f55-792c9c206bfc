from __future__ import annotations

from langgraph.func import task


@task
async def stream_post_task(payload: dict) -> dict:
    """
    LangGraph task：处理一个流式分块（写盘 + 入库）。
    payload:
      - batch_id: str
      - chunk_index: int
      - chunk_text: str
      - book_name: str
      - book_id: str
      - configurable: dict (保留扩展位)
    return: {"index": int, "file": str, "doc_key": Optional[str]}
    """
    from src.nodes.common.node_kit import write_text_under_book
    from src.nodes.common.store_manager import WriterStoreManager

    batch_id = str(payload.get("batch_id") or "unknown")
    idx = int(payload.get("chunk_index") or 0)
    chunk_text = str(payload.get("chunk_text") or "")
    book_name = str(payload.get("book_name") or "")
    book_id = payload.get("book_id")

    # 写入文件（幂等覆盖）
    rel_path = f"chapters_stream/batch-{batch_id}/chunk-{idx + 1:03d}.md"
    try:
        _ = write_text_under_book(book_name, book_id, rel_path, chunk_text)
    except Exception:
        pass

    # 入库（流分块）
    doc_key = None
    try:
        if book_id:
            sm = WriterStoreManager(book_id=book_id)
            doc_key = await sm.store_stream_segment(batch_id=batch_id, chunk_index=idx, content=chunk_text)
    except Exception:
        doc_key = None

    return {"index": idx, "file": rel_path, "doc_key": doc_key}


__all__ = [
    "stream_post_task",
]
