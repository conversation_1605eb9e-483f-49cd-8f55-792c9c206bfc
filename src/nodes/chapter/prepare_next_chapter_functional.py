from typing import Literal, Optional

from langgraph.config import RunnableConfig
from langgraph.types import Command

from src.context.context_builder import ContextBuilder
from src.nodes.common.book_types import BookDetail, BookSceneDesign, BookSumary, Chapter
from src.nodes.common.node_kit import build_resume_info, llm_json, write_text_under_book
from src.state import State


class NextStepDecision(Chapter):
    """
    判定结果：是否切换场景。
    仅作为JSON输出承载：
      - title: str (沿用本章标题提示下一章标题草案，可为空)
      - 分章正文: str （忽略）
      - 剩余文字: str （忽略）
      - need_switch_scene: bool
      - reason: str
    """

    need_switch_scene: bool
    reason: str = ""


async def prepare_next_chapter_fn(
    state: State, config: RunnableConfig
) -> Command[Literal["stream_write", "writer_scene", "prepare_next_chapter"]]:
    """
    在每章完成后，汇总进展，判断是否需要切换场景/进入新一轮场景设计。
    - 若当前卷的场景写完：goto writer_scene
    - 否则：need_switch_scene=True -> 标记切换状态；False -> 继续IN_SCENE
    """
    book_detail: Optional[BookDetail] = state.get("book_detail")
    scene_design: Optional[BookSceneDesign] = state.get("scene_design")

    if not book_detail or not getattr(book_detail, "chapters", None):
        raise AssertionError("❌ 缺少章节数据，无法评估下一章")

    current_idx = len(book_detail.chapters)

    # 1) 卷场景是否已完成：若当前卷没有场景或已消费完，直接进入 writer_scene 生成下一轮
    try:
        vol_number = (current_idx - 1) // 100 + 1
        vs = scene_design.get_volume_scenes(vol_number) if scene_design else None
        if not vs or not vs.scenes:
            update = {
                "writing_state": "SWITCHING_SCENE",
                "resume_info": build_resume_info(
                    state,
                    node="prepare_next_chapter",
                    next_node="writer_scene",
                    summary="场景：当前卷无可用场景 -> writer_scene",
                    volume_number=vol_number,
                ),
            }
            return Command(update=update, goto="writer_scene")
        # 粗略估算是否写完该卷场景（基于estimated_chapters总和 vs 当前章号）
        est_total = sum([max(1, s.estimated_chapters or 1) for s in vs.scenes])
        chapter_in_vol = ((current_idx - 1) % 100) + 1
        if chapter_in_vol >= est_total:
            update = {
                "writing_state": "SWITCHING_SCENE",
                "resume_info": build_resume_info(
                    state,
                    node="prepare_next_chapter",
                    next_node="writer_scene",
                    summary=f"场景：卷{vol_number}预计场景已完成 -> writer_scene",
                    volume_number=vol_number,
                ),
            }
            return Command(update=update, goto="writer_scene")
    except Exception:
        pass

    # 2) 生成 L1/L2/L3 摘要并决定是否切换场景
    last_ch = book_detail.chapters[-1]

    # 获取或创建 BookSumary
    book_summary: BookSumary | None = state.get("book_summary")
    if book_summary is None:
        book_summary = BookSumary()

    # 2.1 L1 摘要（每章）
    cb_l1 = ContextBuilder().header()
    cb_l1.section("任务", "请用一段话总结本章的关键推进、冲突与角色变化，100字内，纯正文。")
    cb_l1.section("本章内容", last_ch.get_completed_content())

    from pydantic import BaseModel, Field as PydField

    class SumL1(BaseModel):
        summary_l1: str = PydField(..., description="每章简短摘要")

    cb_l1.json_schema(SumL1.model_json_schema())
    l1_msg = cb_l1.build(
        max_tokens=config.get("configurable", {}).get("prompt_budget_tokens"), policy="chapter_segment"
    )
    l1 = await llm_json("prepare_next_chapter:l1", l1_msg, SumL1, config=config)
    l1_text = getattr(l1, "summary_l1", "")
    book_summary.set_l1(last_ch.chapter_number, l1_text)
    # 落盘 L1
    try:
        file_rel = f"summary/L1/第{last_ch.chapter_number}章-L1.md"
        write_text_under_book(state.get("book_name"), state.get("book_id"), file_rel, l1_text)
    except Exception:
        pass

    # 2.2 L2 摘要（每10章一次）
    need_l2 = last_ch.chapter_number % 10 == 0
    if need_l2:
        cb_l2 = ContextBuilder().header()
        cb_l2.section("任务", "请总结最近10章的剧情进展、主线变化、伏笔回收与新悬念，300字内，纯正文。")
        ten_start = last_ch.chapter_number - 9
        block_contents = []
        for n in range(ten_start, last_ch.chapter_number + 1):
            try:
                ch = next(c for c in book_detail.chapters if c.chapter_number == n)
                block_contents.append(ch.get_completed_content())
            except StopIteration:
                pass
        cb_l2.section("近10章正文", "\n\n".join(block_contents))

        class SumL2(BaseModel):
            summary_l2: str = PydField(..., description="10章摘要")

        cb_l2.json_schema(SumL2.model_json_schema())
        l2_msg = cb_l2.build(
            max_tokens=config.get("configurable", {}).get("prompt_budget_tokens"), policy="chapter_segment"
        )
        l2 = await llm_json("prepare_next_chapter:l2", l2_msg, SumL2, config=config)
        l2_text = getattr(l2, "summary_l2", "")
        book_summary.set_l2_for_chapter(last_ch.chapter_number, l2_text)
        # 落盘 L2（块索引）
        try:
            l2_idx = BookSumary.block10_index(last_ch.chapter_number)
            file_rel = f"summary/L2/块{l2_idx}-10章.md"
            write_text_under_book(state.get("book_name"), state.get("book_id"), file_rel, l2_text)
        except Exception:
            pass

    # 2.3 L3 摘要（每50章一次）
    need_l3 = last_ch.chapter_number % 50 == 0
    if need_l3:
        cb_l3 = ContextBuilder().header()
        cb_l3.section("任务", "请总结最近50章的主线推进、角色弧光、力量/世界观进阶与关键转折，800字内，纯正文。")
        fifty_start = last_ch.chapter_number - 49
        block_contents = []
        for n in range(fifty_start, last_ch.chapter_number + 1):
            try:
                ch = next(c for c in book_detail.chapters if c.chapter_number == n)
                block_contents.append(ch.get_completed_content())
            except StopIteration:
                pass
        cb_l3.section("近50章正文", "\n\n".join(block_contents))

        class SumL3(BaseModel):
            summary_l3: str = PydField(..., description="50章摘要")

        cb_l3.json_schema(SumL3.model_json_schema())
        l3_msg = cb_l3.build(
            max_tokens=config.get("configurable", {}).get("prompt_budget_tokens"), policy="chapter_segment"
        )
        l3 = await llm_json("prepare_next_chapter:l3", l3_msg, SumL3, config=config)
        l3_text = getattr(l3, "summary_l3", "")
        book_summary.set_l3_for_chapter(last_ch.chapter_number, l3_text)
        # 落盘 L3（块索引）
        try:
            l3_idx = BookSumary.block50_index(last_ch.chapter_number)
            file_rel = f"summary/L3/块{l3_idx}-50章.md"
            write_text_under_book(state.get("book_name"), state.get("book_id"), file_rel, l3_text)
        except Exception:
            pass

    # 2.4 让 LLM 评估是否切换场景
    cb = ContextBuilder()
    cb.header()
    progress = last_ch.to_next_chapter_prompt()
    scene_hint = ""
    try:
        if scene_design:
            curr_scene = scene_design.get_scene_for_chapter(current_idx + 1)
            if curr_scene:
                scene_hint = curr_scene.get_scene_content()
    except Exception:
        scene_hint = ""

    # 注入最近的摘要，帮助模型把握上下文
    recent_l1 = book_summary.get_recent_l1(5)
    l1_text = "\n".join([f"第{n}章：{s}" for n, s in recent_l1]) if recent_l1 else ""
    l2_latest = book_summary.get_latest_l2()
    l3_latest = book_summary.get_latest_l3()

    cb.section("已写内容要点", progress)
    if l1_text:
        cb.section("最近章节摘要（L1）", l1_text)
    if l2_latest:
        cb.section("最近10章摘要（L2）", f"块{l2_latest[0]}：{l2_latest[1]}")
    if l3_latest:
        cb.section("最近50章摘要（L3）", f"块{l3_latest[0]}：{l3_latest[1]}")
    cb.section("下一个场景提示", scene_hint or "无")
    cb.section(
        "判定任务",
        "请判断：是否应当切换到下一个场景？若上一章已收束当前场景冲突且下个场景铺垫充分，则切换；否则继续在当前场景推进。",
    )

    class SwitchDecision(BaseModel):
        need_switch_scene: bool = PydField(..., description="是否需要切换场景")
        reason: str = PydField("", description="简要理由")

    cb.json_schema(SwitchDecision.model_json_schema())
    messages = cb.build(max_tokens=config.get("configurable", {}).get("prompt_budget_tokens"), policy="scene")

    decision = await llm_json("prepare_next_chapter", messages, SwitchDecision, config=config)

    # 汇总更新写回 state
    update_base = {"book_summary": book_summary}

    if getattr(decision, "need_switch_scene", False):
        update = {
            **update_base,
            "writing_state": "SWITCHING_SCENE",
            "resume_info": build_resume_info(
                state,
                node="prepare_next_chapter",
                next_node="stream_write",
                summary="判定：切换场景 -> 过渡写作",
            ),
        }
        return Command(update=update, goto="stream_write")
    else:
        update = {
            **update_base,
            "writing_state": "IN_SCENE",
            "resume_info": build_resume_info(
                state,
                node="prepare_next_chapter",
                next_node="stream_write",
                summary="判定：继续当前场景 -> 流式写作",
            ),
        }
        return Command(update=update, goto="stream_write")
