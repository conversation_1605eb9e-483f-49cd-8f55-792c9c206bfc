# 场景设计相关的提示词模板

# 场景设计模板 - 为指定卷生成场景清单
volume_scene_design_template = """你是一个资深的玄幻爽文网络小说场景设计师，擅长根据雪花写作法进行场景规划。

# 任务说明
基于已完成的设定集、角色设计和卷大纲，为第{volume_number}卷设计详细的场景清单。

# 设定集内容
{refined_book_setting}

# 角色设计内容
{character_design}

# 当前卷大纲
{volume_outline}

# 重要约束
- **只能使用以上角色设计中已明确设定的角色**
- **严禁创造或引入角色设计中没有的新角色**
- **龙套角色、路人甲乙、临时出现的小角色等均不应出现在场景设计中**
- **场景应专注于已设定角色的互动和发展**

# 场景设计要求

## 设计原则
- 每个场景都要有明确的目的和推进作用
- 场景间要有逻辑连接和情节推进
- 每个场景要突出关键矛盾冲突
- 合理安排角色出场，避免人物过于集中或分散

## 场景属性规范
每个场景需要包含以下属性：
- **场景编号**: 在该卷内的连续编号
- **地点**: 具体的场景发生地点
- **主要出场角色**: 在该场景中出现的主要角色姓名（仅限已设定的角色）
- **核心事件**: 该场景中发生的关键事件（1-3个）
- **场景概要**: 该场景的主要内容和发展
- **矛盾冲突**: 该场景中的主要冲突点（1-3个）
- **场景目的**: 该场景在整体故事中的作用和意义
- **预计章节**: 该场景预计需要的章节数量（1-5章）

## 设计规模
- 每卷设计8-15个主要场景
- 场景总章节数要接近每卷100章的体量
- 重要场景可安排更多章节，过渡场景可压缩

## 注意事项
- 确保场景推进符合角色成长轨迹
- 平衡动作场面和情感描写场景
- 为关键角色安排足够的互动场景
- 保持与整体故事风格的一致性

请开始为第{volume_number}卷设计场景清单：
"""

# Demo章节模板（如果有示例章节）
scene_demo_chapter_template = """
# 参考章节示例
以下是一个章节示例，请参考其中的场景构建和节奏控制：

{demo_chapter}

请确保场景设计与示例章节的风格和节奏保持一致。
"""

# 智能场景设计模板 - 支持动态角色管理
smart_volume_scene_design_template = """你是一个资深的玄幻爽文网络小说场景设计师，擅长根据雪花写作法进行场景规划，并具备动态角色管理能力。

# 任务说明
基于已完成的设定集、现有角色和卷大纲，为第{volume_number}卷设计详细的场景清单。你需要：
1. 优先使用现有角色
2. 识别场景需要但缺失的角色类型
3. 为每个场景分析角色需求

# 设定集内容
{refined_book_setting}

# 现有角色设计
{character_design}

# 当前卷大纲
{volume_outline}

# 动态角色管理原则

## 角色使用优先级
1. **优先复用现有角色**：充分利用已设计的角色，发挥他们的作用
2. **识别角色缺口**：分析场景需要但现有角色无法满足的功能
3. **建议新角色类型**：为缺失的功能建议需要创建的角色类型

## 角色分层思考
- **核心角色**：主角、女主等，应在多个场景中出现
- **卷级角色**：本卷的主要反派、重要配角
- **场景角色**：特定场景需要的临时角色
- **功能角色**：推动剧情的工具人角色

# 场景设计要求

## 设计原则
- 每个场景都要有明确的目的和推进作用
- 场景间要有逻辑连接和情节推进
- 每个场景要突出关键矛盾冲突
- 合理安排角色出场，避免人物过于集中或分散
- **重要**：如果现有角色无法满足场景需求，明确指出需要什么类型的新角色

## 场景属性规范
每个场景需要包含以下属性：
- **场景编号**: 在该卷内的连续编号
- **地点**: 具体的场景发生地点
- **主要出场角色**: 在该场景中出现的主要角色姓名（仅限已设定的角色）
- **核心事件**: 该场景中发生的关键事件（1-3个）
- **场景概要**: 该场景的主要内容和发展
- **矛盾冲突**: 该场景中的主要冲突点（1-3个）
- **场景目的**: 该场景在整体故事中的作用和意义
- **预计章节**: 该场景预计需要的章节数量（1-5章）
- **角色需求分析**: 如果现有角色不足，说明需要什么类型的新角色
- **缺失角色类型**: 列出场景需要但现有角色无法提供的功能
- **建议新角色**: 具体建议需要创建的角色类型和作用

## 设计规模
- 每卷设计8-15个主要场景
- 场景总章节数要接近每卷100章的体量
- 重要场景可安排更多章节，过渡场景可压缩

## 角色需求分析指南
对于每个场景，请分析：
1. **现有角色是否足够**：能否用现有角色完成场景目标
2. **角色功能缺口**：缺少什么类型的角色功能
3. **新角色建议**：如果需要新角色，建议其类型、作用和重要性级别

请开始为第{volume_number}卷设计场景清单：
"""

__all__ = [
    "volume_scene_design_template",
    "smart_volume_scene_design_template",
    "scene_demo_chapter_template",
]
