from __future__ import annotations

from langgraph.func import task

# no Command/Send used here
from src.context.context_builder import Context<PERSON>uilder
from src.nodes.common.book_types import VolumeSceneDesign
from src.nodes.scene.scene_prompt import smart_volume_scene_design_template, volume_scene_design_template


@task
async def scene_design_task(payload: dict) -> dict:
    """
    LangGraph task：细化单卷场景设计并落盘（副作用仅执行一次）。
    payload:
      - volume_number: int
      - refined_book_setting: str
      - character_content: str
      - current_volume_outline_text: str
      - book_name: str
      - book_id: str
      - configurable: dict
    return: {"volume_number": int, "refined": dict, "file_rel": str}
    """
    from src.nodes.common.node_kit import llm_json, write_text_under_book

    vol_number = int(payload.get("volume_number"))
    refined_book_setting = payload.get("refined_book_setting", "")
    character_content = payload.get("character_content", "")
    outline_text = payload.get("current_volume_outline_text", "")

    enable_smc = (payload.get("configurable") or {}).get("enable_smart_character_management", True)
    append_mode = bool(payload.get("append_mode") or False)
    target_needed_scenes = int(payload.get("target_needed_scenes") or 0)
    existing_scenes_count = int(payload.get("existing_scenes_count") or 0)

    base_prompt = (smart_volume_scene_design_template if enable_smc else volume_scene_design_template).format(
        volume_number=vol_number,
        refined_book_setting=refined_book_setting,
        character_design=character_content,
        volume_outline=outline_text,
    )

    if append_mode and target_needed_scenes > 0:
        extra = (
            f"\n\n请仅生成接下来 {target_needed_scenes} 个场景，"
            f"场景编号从 {existing_scenes_count + 1} 开始连续编号，不要重复已有场景，不要生成超过要求数量。"
        )
        scene_prompt = base_prompt + extra
    else:
        scene_prompt = base_prompt

    cb = ContextBuilder()
    cb.header()
    cb.section("场景设计任务", scene_prompt)
    cb.json_schema(VolumeSceneDesign.model_json_schema())
    messages = cb.build(max_tokens=None, policy="scene")

    config = {"configurable": payload.get("configurable", {})}
    refined_one: VolumeSceneDesign = await llm_json("writer_scene", messages, VolumeSceneDesign, config=config)
    if not refined_one.scenes:
        return {"volume_number": vol_number, "refined": None, "file_rel": None}
    refined_one.volume_number = vol_number
    if append_mode:
        refined_one.design_completed = False
    else:
        refined_one.design_completed = True

    # 落盘
    book_name = payload.get("book_name", "")
    book_id = payload.get("book_id", "")
    file_rel_one = f"scene/第{vol_number}卷场景清单.md"
    if not append_mode:
        try:
            _ = write_text_under_book(book_name, book_id, file_rel_one, refined_one.get_scenes_content())
        except Exception:
            pass

    return {
        "volume_number": vol_number,
        "refined": refined_one.model_dump(),
        "file_rel": (None if append_mode else file_rel_one),
    }


__all__ = [
    "scene_design_task",
]
