import logging
from typing import Any, Dict, List, Optional, Tuple

from langchain_core.messages import BaseMessage

from src.common.llm_config import ModelTier
from src.common.utils.llm_request_utils import llm_str_request, llm_structured_request
from src.nodes.common.book_types import Chapter
from src.nodes.common.store_manager import WriterStoreManager


logger = logging.getLogger(__name__)


def _get_configurable(config: dict) -> dict:
    """稳健获取 RunnableConfig.configurable。

    兼容：
    - 原始 dict: {"configurable": {...}}
    - Mapping 风格（如 RunnableConfig）: obj.get("configurable", {})
    - 属性风格: obj.configurable
    任何异常均返回空 dict。
    """
    try:
        # 1) 普通 dict
        if isinstance(config, dict):
            return config.get("configurable", {}) or {}
        # 2) Mapping.get 接口
        get_attr = getattr(config, "get", None)
        if callable(get_attr):
            try:
                cfg = config.get("configurable", {})  # type: ignore[attr-defined]
            except Exception:
                cfg = {}
            return (cfg or {}) if isinstance(cfg, dict) else (dict(cfg) if cfg else {})
        # 3) 属性访问
        cfg = getattr(config, "configurable", None)
        return (cfg or {}) if isinstance(cfg, dict) else (dict(cfg) if cfg else {})
    except Exception:
        return {}


def tier_of(config: dict) -> dict:
    cfg = _get_configurable(config)
    return {
        "tier": cfg.get("model_tier"),
        "llm_params": cfg.get("llm_params", {}),
    }


async def llm_json(
    node_name: str,
    messages: List[BaseMessage],
    schema_type: type,
    *,
    config: dict,
    stage_name: Optional[str] = None,
    tier_override: Optional[str] = None,
) -> Any:
    params = tier_of(config)
    tier_value = params.get("tier")
    if tier_override is not None:
        tier_value = tier_override
    if isinstance(tier_value, str):
        lowered = tier_value.lower().strip()
        if lowered in ("low", "medium", "high"):
            tier_value = ModelTier[lowered.upper()]

    # 首次请求
    _, parsed, _ = await llm_structured_request(
        node_name=node_name,
        input_messages=messages,
        schema_type=schema_type,
        stage_name=stage_name,
        tier=tier_value,
        llm_params=params.get("llm_params", {}),
    )
    return parsed


async def llm_text(
    node_name: str,
    messages: List[BaseMessage],
    *,
    config: dict,
    stage_name: Optional[str] = None,
    tier_override: Optional[str] = None,
) -> Tuple[str, str]:
    params = tier_of(config)
    tier_value = params.get("tier")
    if tier_override is not None:
        tier_value = tier_override
    if isinstance(tier_value, str):
        lowered = tier_value.lower().strip()
        if lowered in ("low", "medium", "high"):
            tier_value = ModelTier[lowered.upper()]

    # 首次请求
    content, cot = await llm_str_request(
        node_name=node_name,
        input_messages=messages,
        stage_name=stage_name,
        tier=tier_value,
        llm_params=params.get("llm_params", {}),
    )
    return content, cot


# -------------------- 文件系统工具 --------------------


def write_text_under_book(book_name: Optional[str], book_id: Optional[str], rel_path: str, content: str) -> str:
    """
    在书籍目录下写入文本文件。
    - 若 book_name 为空，则直接写入 workspace 根目录
    - 返回相对于 workspace 的文件路径
    """
    from src.common.utils.filesystem_utils import FileSystemUtils

    if book_name:
        full_rel = f"{book_name}/{rel_path}"
    else:
        full_rel = rel_path

    FileSystemUtils.write_content_to_workspace(full_rel, content)
    return full_rel


def write_batch_under_book(book_name: Optional[str], book_id: Optional[str], files: List[Tuple[str, str]]) -> List[str]:
    """
    批量写入文件到书籍目录下。

    Args:
        book_name: 书籍名称
        book_id: 书籍ID
        files: (相对路径, 内容) 的列表

    Returns:
        写入的文件路径列表
    """
    written_files = []
    for rel_path, content in files:
        file_path = write_text_under_book(book_name, book_id, rel_path, content)
        written_files.append(file_path)
    return written_files


def rel_path_for_chapter(chapter_number: int, chapter_title: str) -> str:
    """
    生成章节文件的相对路径

    Args:
        chapter_number: 章节编号
        chapter_title: 章节标题

    Returns:
        章节文件的相对路径，格式为 "chapter/第N章-标题.md"
    """
    # 清理标题中的特殊字符
    safe_title = chapter_title.replace("/", "_").replace("\\", "_").replace(":", "_")
    return f"chapter/第{chapter_number}章-{safe_title}.md"


# -------------------- 数据库存储工具 --------------------


async def save_plan_to_db(plan: Any, book_id: Optional[str]) -> None:
    """保存计划到数据库"""
    if not book_id or not plan:
        return
    manager = WriterStoreManager(book_id=book_id)
    await manager.store_writer_plan(plan=plan)


async def save_chapter_to_db(chapter: Chapter, book_id: Optional[str]) -> None:
    """保存章节到数据库"""
    if not book_id or not chapter:
        return
    try:
        manager = WriterStoreManager(book_id=book_id)
        await manager.store_chapter(chapter)
    except Exception as e:
        logger.warning(f"章节入库失败: {e}")


# -------------------- ResumeInfo 统一构造器 --------------------


def build_resume_info(
    state: dict,
    *,
    node: str,
    next_node: str,
    summary: Optional[str] = None,
    **extra_fields: Any,
) -> list[dict]:
    """构造并返回本次追加的 resume_info 增量（列表形式，便于 reducer 聚合）。"""
    info: Dict[str, Any] = {
        "node": node,
        "next": next_node,
        "book_id": state.get("book_id"),
        "book_name": state.get("book_name"),
    }
    if summary:
        info["summary"] = summary
    for k, v in (extra_fields or {}).items():
        if v is not None:
            info[k] = v
    return [info]


def build_stream_resume_info(
    state: dict,
    *,
    node: str,
    next_node: str,
    buffer_len: int,
    chunks_count: Optional[int] = None,
    summary: Optional[str] = None,
    **extra_fields: Any,
) -> list[dict]:
    """
    流式写作/后处理相关的 ResumeInfo 构造器：返回增量，供 reducer 聚合。
    默认摘要：
      - 若提供 chunks_count："流式写作：缓冲{buffer_len}字 → 后处理{chunks_count}块"
      - 否则："流式写作：缓冲{buffer_len}字 -> {next_node}"
    始终写入 buffer_len 字段；可追加自定义字段（如 post_batch_id）。
    """
    if summary is None:
        if chunks_count is not None:
            summary = f"流式写作：缓冲{buffer_len}字 → 后处理{chunks_count}块"
        else:
            summary = f"流式写作：缓冲{buffer_len}字 -> {next_node}"
    fields = dict(extra_fields or {})
    fields.setdefault("buffer_len", buffer_len)
    info: Dict[str, Any] = {
        "node": node,
        "next": next_node,
        "book_id": state.get("book_id"),
        "book_name": state.get("book_name"),
        "buffer_len": buffer_len,
    }
    if summary:
        info["summary"] = summary
    for k, v in fields.items():
        if v is not None:
            info[k] = v
    return [info]


# -------------------- 审核占位（已统一自动直通） --------------------


def maybe_interrupt_review(
    *,
    state: dict,
    config: dict,
    node_name: str,
    input_messages: Optional[List[BaseMessage]] = None,
    review_file_name: Optional[str] = None,
    review_content: Optional[str] = None,
    next_node_if_pass: Optional[str] = None,
) -> tuple[dict, str]:
    """
    占位实现：人工审核已在 v2/v3 流程中移除，统一自动直通。

    保留该函数用于兼容现有调用与单测断言：
    - 返回空 update 与下一节点（默认 `next_node_if_pass` 或原节点）。
    - 不修改 state，不写入 skip 计数，不阻塞流程。
    """
    try:
        cfg = {}
        if isinstance(config, dict):
            cfg = config.get("configurable", {}) or {}
        else:
            cfg = getattr(config, "configurable", {}) or {}
        # 如显式开启审核，也按直通处理，以保持“功能不变/不中断”。
        _ = cfg.get("enable_interrupt_review")
    except Exception:
        pass

    next_node = next_node_if_pass or str(node_name or "").strip() or "__end__"
    return {}, next_node
