"""
小说写作系统的核心数据模型定义

本模块包含了小说写作AI系统中所有的数据模型，采用领域驱动设计（DDD）原则：

模型分类：
1. **计划模型**：WriterPlan、WriterPlanStep - 书籍创作计划和步骤
2. **角色模型**：CharacterSummary、CharacterDetail、CharacterCollection - 角色设计数据
3. **大纲模型**：VolumeOutline、ChapterOutline、BookDetail - 书籍结构和大纲
4. **场景模型**：Scene、VolumeSceneDesign、BookSceneDesign - 场景设计数据
5. **章节模型**：Chapter - 具体章节内容

设计原则：
- 所有模型继承自LangChain的Serializable，支持序列化和持久化
- 使用Pydantic进行数据验证和类型检查
- 提供丰富的业务方法，封装领域逻辑
- 支持内容格式化和展示功能
- 实现组合模式，支持复杂数据结构

扩展指南：
- 新增模型时继承Serializable并实现序列化方法
- 为模型添加业务方法来封装领域逻辑
- 使用Field()添加详细的字段描述
- 在APP_SERIALIZABLE_MAPPINGS中注册新模型
"""

import logging
from typing import Annotated, Any, Dict, List, Literal, Optional, override, Tuple

from langchain_core.load.serializable import Serializable
from langchain_core.tools import InjectedToolArg
from pydantic import Field

logger = logging.getLogger(__name__)


# 写作计划步骤状态类型定义
# 状态流转：draft → refined → reviewing → done
# - draft: 初稿状态，步骤已执行但未精炼
# - refined: 已精炼状态，步骤内容已优化但可能正在审核
# - reviewing: 审核中状态，正在进行人工审核
# - done: 完成状态，步骤完全完成
StepStatus = Literal["draft", "refined", "reviewing", "done"]


class WriterPlanStepSchema(Serializable):
    """
    写作计划步骤模式类 - 用于LLM生成阶段的数据结构

    业务含义：
    - 表示书籍创作计划中的单个步骤的基础信息
    - 用于LLM生成WriterPlan时的中间数据结构
    - 包含步骤的标题和描述，不包含执行状态

    使用场景：
    - bs_planner_node中作为LLM输出的schema
    - 通过convert_to_writer_plan()转换为完整的WriterPlan
    """

    step_title: str = Field(..., description="步骤标题")
    step_description: str = Field(..., description="步骤描述")

    @classmethod
    def is_lc_serializable(cls) -> bool:
        """Return whether this model can be serialized by Langchain."""
        return True

    @classmethod
    def get_lc_namespace(cls) -> List[str]:
        """Get the namespace of the langchain object."""
        return ["src", "nodes", "common", "book_types"]


class WriterPlanStep(Serializable):
    """
    写作计划步骤类 - 完整的步骤执行状态管理

    业务含义：
    - 表示书籍创作计划中的单个步骤，包含完整的执行状态
    - 支持步骤的执行、完成和精炼状态跟踪
    - 提供步骤内容的格式化和展示功能

    状态管理：
    - step_execution_res: 步骤执行结果，None表示未执行
    - step_status: 步骤状态，支持 draft/refined/reviewing/done

    业务方法：
    - get_step_content(): 获取格式化的步骤内容
    - needs_refinement(): 检查是否需要精炼
    - is_in_review(): 检查是否正在审核中
    - is_completed(): 检查是否完全完成
    """

    step_title: str = Field(..., description="步骤标题")
    step_description: str = Field(..., description="步骤描述")
    # 历史数据兼容：允许为 None
    step_execution_res: Annotated[Optional[str], InjectedToolArg] = None
    step_status: Annotated[StepStatus, InjectedToolArg] = "draft"

    @classmethod
    def is_lc_serializable(cls) -> bool:
        """Return whether this model can be serialized by Langchain."""
        return True

    @classmethod
    def get_lc_namespace(cls) -> List[str]:
        """Get the namespace of the langchain object."""
        return ["src", "nodes", "common", "book_types"]

    def get_step_content(self, prefix_index: Optional[int] = None) -> str:
        """获取步骤内容，包括执行结果"""
        step_contents = []
        # 步骤标题和描述
        if prefix_index:
            step_contents.append(f"# {prefix_index}. {self.step_title} \n {self.step_description}")
        else:
            step_contents.append(f"# {self.step_title} \n {self.step_description}")

        # 如果有执行结果，添加执行结果
        if self.step_execution_res:
            step_contents.append(f"{self.step_execution_res.replace('# ', '## ')}")

        step_contents_str = "\n\n".join(step_contents)
        return step_contents_str

    def needs_refinement(self) -> bool:
        """检查步骤是否需要精炼"""
        return self.step_execution_res and self.step_status == "draft"

    def is_in_review(self) -> bool:
        """检查步骤是否正在审核中"""
        return self.step_status == "reviewing"

    def is_completed(self) -> bool:
        """检查步骤是否完全完成"""
        return self.step_status == "done"

    def is_refined(self) -> bool:
        """检查步骤是否已精炼"""
        return self.step_status in ["refined", "reviewing", "done"]

    def set_status(self, status: StepStatus) -> None:
        """设置步骤状态"""
        self.step_status = status


class WriterPlanSchema(Serializable):
    book_name: Annotated[str, "书名"]
    book_description: Annotated[str, "小说简介"]
    plan_thought: Annotated[str, "首先，清晰地理解自己的任务目标作"]
    plan_steps: List[WriterPlanStepSchema] = Field(default_factory=list, description="计划的步骤列表")

    @classmethod
    def is_lc_serializable(cls) -> bool:
        """Return whether this model can be serialized by Langchain."""
        return True

    @classmethod
    def get_lc_namespace(cls) -> List[str]:
        """Get the namespace of the langchain object."""
        return ["src", "nodes", "common", "book_types"]

    def convert_to_writer_plan(self) -> "WriterPlan":
        return WriterPlan(
            book_name=self.book_name,
            book_description=self.book_description,
            plan_thought=self.plan_thought,
            plan_steps=[
                WriterPlanStep(
                    step_title=step.step_title,
                    step_description=step.step_description,
                )
                for step in self.plan_steps
            ],
        )


class WriterPlan(Serializable):
    plan_thought: Annotated[str, "首先，清晰地理解自己的任务目标"]
    plan_steps: List[WriterPlanStep] = Field(default_factory=list, description="计划的步骤列表")
    book_name: Annotated[str, "书名"]
    book_description: Annotated[str, "小说简介"]

    @classmethod
    def is_lc_serializable(cls) -> bool:
        """Return whether this model can be serialized by Langchain."""
        return True

    @classmethod
    def get_lc_namespace(cls) -> List[str]:
        """Get the namespace of the langchain object."""
        return ["src", "nodes", "common", "book_types"]

    def is_all_step_completed(self) -> bool:
        """检查所有步骤是否已完成"""
        for step in self.plan_steps:
            if not step.step_execution_res:
                return False
        return True

    def is_all_step_refined(self) -> bool:
        """检查所有步骤是否已精炼"""
        for step in self.plan_steps:
            if not step.is_refined():
                return False
        return True

    def find_next_step(self) -> Tuple[int, bool]:
        """
        查找下一个未执行的步骤

        Returns:
            Tuple[int, bool]:
            - 步骤索引
            - 是否是最后一个步骤
        """
        total_steps = len(self.plan_steps)
        completed_steps = 0

        # 统计已完成的步骤数
        for step in self.plan_steps:
            if step.step_execution_res:
                completed_steps += 1

        # 查找下一个未执行的步骤
        for step_index, step in enumerate(self.plan_steps):
            if not step.step_execution_res:
                # 判断这是否是最后一个未完成的步骤
                is_last_step = (completed_steps + 1) == total_steps
                return step_index, is_last_step

        # 所有步骤都已完成
        return None, None

    def get_completed_contents(self) -> str:
        """获取已完成的内容"""
        completed_contents = []
        completed_contents.append(f"# {self.book_name}")
        completed_contents.append(f"{self.book_description}")
        completed_contents.append(f"<plan_thought>\n{self.plan_thought}\n</plan_thought>")
        # 步骤
        for i, step in enumerate(self.plan_steps, 1):
            completed_contents.append(step.get_step_content(prefix_index=i).replace("# ", "## "))

        completed_contents_str = "\n\n".join(completed_contents)
        return completed_contents_str

    def get_unrefined_contents(self) -> str:
        """获取未精炼的内容"""
        unrefined_contents = []
        unrefined_contents.append(f"# {self.book_name}")
        unrefined_contents.append(f"{self.book_description}")
        unrefined_contents.append(f"<plan_thought>\n{self.plan_thought}\n</plan_thought>")
        # 步骤
        for i, step in enumerate(self.plan_steps, 1):
            if step.is_refined():
                continue
            unrefined_contents.append(step.get_step_content(prefix_index=i).replace("# ", "## "))

        unrefined_contents_str = "\n\n".join(unrefined_contents)
        return unrefined_contents_str

    def get_last_step_contents(self) -> str:
        """获取最后一个已完成步骤的内容"""
        temp_result = ""
        for step in self.plan_steps:
            if not step.step_execution_res:
                return temp_result
            else:
                temp_result = step.step_execution_res
        return temp_result

    def get_json_contents(self) -> str:
        return self.model_dump_json(indent=4, exclude_none=False)

    def get_next_node(self) -> str:
        if any(step.needs_refinement() for step in self.plan_steps):
            return "bs_refine"
        else:
            return "bs_steps"


class ChapterOutline(Serializable):
    """章节大纲、内容"""

    volume_number: int = Field(..., description="所属卷编号")
    chapter_number: int = Field(..., description="章节编号，从1开始，换卷不重置")
    chapter_title: str = Field(..., description="不含编号的章节标题")
    chapter_summary: str = Field(..., description="章节概要")
    key_events: List[str] = Field(default_factory=list, description="关键事件列表")
    featured_characters: List[str] = Field(default_factory=list, description="主要出场角色（仅包含已设定的角色）")

    @classmethod
    def is_lc_serializable(cls) -> bool:
        """Return whether this model can be serialized by Langchain."""
        return True

    @classmethod
    def get_lc_namespace(cls) -> List[str]:
        """Get the namespace of the langchain object."""
        return ["src", "nodes", "common", "book_types"]


class VolumeOutline(Serializable):
    """卷大纲"""

    volume_number: int = Field(..., description="卷编号，从1开始")
    volume_title: str = Field(..., description="不含编号的卷标题")
    volume_outline_content: str = Field(..., description="卷大纲内容")
    featured_characters: List[str] = Field(
        default_factory=list, description="本卷主要出场角色列表（仅包含已设定的角色）"
    )
    version: int = Field(default=0, description="大纲版本号，初始为0，每次细化后+1")

    @classmethod
    def is_lc_serializable(cls) -> bool:
        """Return whether this model can be serialized by Langchain."""
        return True

    @classmethod
    def get_lc_namespace(cls) -> List[str]:
        """Get the namespace of the langchain object."""
        return ["src", "nodes", "common", "book_types"]

    def get_completed_contents(self) -> str:
        completed_contents = []
        completed_contents.append(f"# 第 {self.volume_number} 卷：{self.volume_title}\n")
        if self.featured_characters:
            completed_contents.append(f"**主要出场角色**: {', '.join(self.featured_characters)}\n")
        completed_contents.append(f"{self.volume_outline_content}")
        completed_contents_str = "\n\n".join(completed_contents)
        return completed_contents_str


class BookVolumeOutlinesSchema(Serializable):
    """
    整书分卷大纲的结构化输出Schema：用于初始化阶段生成所有卷的大纲集合。
    """

    volume_outlines: List[VolumeOutline] = Field(default_factory=list, description="卷大纲列表")

    @classmethod
    def is_lc_serializable(cls) -> bool:
        return True

    @classmethod
    def get_lc_namespace(cls) -> List[str]:
        return ["src", "nodes", "common", "book_types"]


class SegmentedChapterResult(Serializable):
    """智能分章返回结构：仅用于分章节点，约束为JSON模式输出。

    字段：
      - title: str
      - 分章正文 -> chapter_content: str
      - 剩余文字 -> remaining_text: str
    """

    # 英文字段名用于内部代码；序列化/校验别名采用中文，便于约束模型输出。
    title: str = Field(..., description="不含编号的章节标题")
    chapter_content: str = Field(
        ..., serialization_alias="分章正文", validation_alias="分章正文", description="本章正文（分章后正文片段）"
    )
    remaining_text: str = Field(
        ..., serialization_alias="剩余文字", validation_alias="剩余文字", description="分章后剩余连续文本"
    )

    @override
    @classmethod
    def is_lc_serializable(cls) -> bool:
        return True

    @override
    @classmethod
    def get_lc_namespace(cls) -> list[str]:
        return ["src", "nodes", "common", "book_types"]


class BookSumary(Serializable):
    """
    写作进展多层次摘要容器：
    - summary_l1_by_chapter: 每章简短摘要（key=章节号）
    - summary_l2_by_block: 每10章摘要（key=块序号，1表示1-10章，2表示11-20章）
    - summary_l3_by_block: 每50章摘要（key=块序号，1表示1-50章，2表示51-100章）
    """

    summary_l1_by_chapter: Dict[int, str] = Field(default_factory=dict)
    summary_l2_by_block: Dict[int, str] = Field(default_factory=dict)
    summary_l3_by_block: Dict[int, str] = Field(default_factory=dict)

    @staticmethod
    def block10_index(chapter_number: int) -> int:
        return (max(1, chapter_number) - 1) // 10 + 1

    @staticmethod
    def block50_index(chapter_number: int) -> int:
        return (max(1, chapter_number) - 1) // 50 + 1

    def set_l1(self, chapter_number: int, summary: str) -> None:
        self.summary_l1_by_chapter[int(chapter_number)] = str(summary or "").strip()

    def set_l2_for_chapter(self, chapter_number: int, summary: str) -> None:
        idx = self.block10_index(chapter_number)
        self.summary_l2_by_block[int(idx)] = str(summary or "").strip()

    def set_l3_for_chapter(self, chapter_number: int, summary: str) -> None:
        idx = self.block50_index(chapter_number)
        self.summary_l3_by_block[int(idx)] = str(summary or "").strip()

    def get_recent_l1(self, k: int = 5) -> list[tuple[int, str]]:
        items = sorted(self.summary_l1_by_chapter.items())
        return items[-k:]

    def get_latest_l2(self) -> tuple[int, str] | None:
        if not self.summary_l2_by_block:
            return None
        idx = max(self.summary_l2_by_block.keys())
        return idx, self.summary_l2_by_block[idx]

    def get_latest_l3(self) -> tuple[int, str] | None:
        if not self.summary_l3_by_block:
            return None
        idx = max(self.summary_l3_by_block.keys())
        return idx, self.summary_l3_by_block[idx]

    @override
    @classmethod
    def is_lc_serializable(cls) -> bool:
        return True

    @override
    @classmethod
    def get_lc_namespace(cls) -> list[str]:
        return ["src", "nodes", "common", "book_types"]


class Chapter(Serializable):
    """章节"""

    chapter_number: int = Field(..., description="章节编号，从1开始，换卷不重置")
    chapter_title: str = Field(..., description="不含编号的章节标题")
    chapter_content: str = Field(
        ..., description="章节内容，每章长度要合理，不要过长或过短"
    )  # TODO 修改结构看能否返回适合的长度

    featured_characters: List[str] = Field(
        default_factory=list,
        description="本章主要出场角色列表（仅包含已设定的角色）",
    )

    think_characters: list[str] = Field(
        default_factory=list,
        description="本章结束后，相关角色的主要信息，写完每章后进行汇总总结，写下一章时进行参考",
    )
    think_items: List[str] = Field(
        default_factory=list,
        description="本章结束后，重要道具、物品的主要信息，写完每章后进行汇总总结，写下一章时进行参考",
    )
    think_content_summary: str = Field(
        default="",
        description="本章结束后，全书已写完内容的汇总信息，写完每章后进行汇总总结，写下一章时进行参考",
    )
    think_todo_outline: str = Field(
        default="",
        description="本章结束后，更新后续几章计划写作的大纲",
    )

    def get_completed_content(self) -> str:
        return f"# 第{self.chapter_number}章 {self.chapter_title}\n{self.chapter_content}"

    def to_next_chapter_prompt(self) -> str:
        return f"""
        <current_info>
        1. 相关角色的主要信息
        {self.think_characters}
        2. 重要道具、物品的主要信息
        {self.think_items}
        3. 全书已写完内容的汇总信息
        {self.think_content_summary}
        4. 后续几章计划写作的大纲
        {self.think_todo_outline}
        </current_info>
        """

    @override
    @classmethod
    def is_lc_serializable(cls) -> bool:
        """Return whether this model can be serialized by Langchain."""
        return True

    @override
    @classmethod
    def get_lc_namespace(cls) -> list[str]:
        """Get the namespace of the langchain object."""
        return ["src", "nodes", "common", "book_types"]


class BookDetail(Serializable):
    """书籍详情"""

    volume_outlines: List[VolumeOutline] = Field(default_factory=list, description="卷大纲列表")
    chapters: List[Chapter] = Field(default_factory=list, description="正文")
    # 流式写作缓冲区：连续生成的内容先进入缓冲区，再进行智能分章
    stream_buffer: str = Field(default="", description="流式写作缓冲区文本")

    @classmethod
    def is_lc_serializable(cls) -> bool:
        """Return whether this model can be serialized by Langchain."""
        return True

    @classmethod
    def get_lc_namespace(cls) -> List[str]:
        """Get the namespace of the langchain object."""
        return ["src", "nodes", "common", "book_types"]

    def get_completed_volume_outlines(self) -> str:
        completed_contents = []
        for vo in self.volume_outlines:
            completed_contents.append(vo.get_completed_contents())
        completed_contents_str = "\n\n".join(completed_contents)
        return completed_contents_str

    def get_volume_outline_for_chapter(
        self, chapter_number: int, chapters_per_volume: int = 100
    ) -> Optional[VolumeOutline]:
        """根据章节编号获取对应的卷大纲"""
        if chapter_number <= 0:
            return None

        volume_index = (chapter_number - 1) // chapters_per_volume
        if volume_index < len(self.volume_outlines):
            return self.volume_outlines[volume_index]
        return None

    def need_volume_outline_refinement(self) -> bool:
        """检查是否需要对卷大纲进行细化"""
        for vo in self.volume_outlines:
            if vo.version == 0:  # 版本为0表示未细化
                return True
        return False

    def get_next_unrefined_volume_index(self) -> Optional[int]:
        """获取下一个需要细化的卷索引"""
        for i, vo in enumerate(self.volume_outlines):
            if vo.version == 0:
                return i
        return None

    def is_outline_completed(self) -> bool:
        """检查大纲是否全部完成（包括细化）"""
        return len(self.volume_outlines) > 0 and not self.need_volume_outline_refinement()

    def need_volume_outline_refinement_for_current_progress(
        self, current_chapter_number: int, chapters_per_volume: int = 100
    ) -> bool:
        """
        基于当前章节进度检查是否需要更多大纲细化
        策略：当前章节所属卷+后面2卷都有细化大纲后，则认为大纲足够

        Args:
            current_chapter_number: 当前章节编号（从1开始）
            chapters_per_volume: 每卷的章节数量，默认100章

        Returns:
            bool: 是否需要更多大纲细化
        """
        if current_chapter_number <= 0:
            return True

        # 计算当前章节所属的卷编号（从1开始）
        current_volume_number = (current_chapter_number - 1) // chapters_per_volume + 1

        # 计算需要细化的最大卷编号：当前卷+后面2卷
        max_required_volume = current_volume_number + 2

        # 检查从当前卷到max_required_volume的所有卷是否都已细化
        for volume_number in range(current_volume_number, max_required_volume + 1):
            # 查找对应的卷大纲
            volume_outline = None
            for vo in self.volume_outlines:
                if vo.volume_number == volume_number:
                    volume_outline = vo
                    break

            # 如果卷不存在或未细化，则需要更多细化
            if volume_outline is None or volume_outline.version == 0:
                return True

        return False

    def is_outline_ready_for_writing(self, current_chapter_number: int, chapters_per_volume: int = 100) -> bool:
        """
        检查大纲是否足够支持当前章节的写作

        Args:
            current_chapter_number: 当前章节编号（从1开始）
            chapters_per_volume: 每卷的章节数量，默认100章

        Returns:
            bool: 大纲是否足够支持写作
        """
        return len(self.volume_outlines) > 0 and not self.need_volume_outline_refinement_for_current_progress(
            current_chapter_number, chapters_per_volume
        )


# ==================== 角色管理相关枚举和模型 ====================


CharacterTier = Literal["core", "volume", "scene", "functional"]


class CharacterLifecycle(Serializable):
    """角色生命周期管理"""

    first_appearance_volume: int = Field(..., description="首次出场的卷编号")
    first_appearance_chapter: Optional[int] = Field(None, description="首次出场的章节编号")
    active_volumes: List[int] = Field(default_factory=list, description="活跃的卷编号列表")
    development_milestones: List[str] = Field(default_factory=list, description="角色发展的关键节点")
    exit_volume: Optional[int] = Field(None, description="退场的卷编号（如果有）")
    exit_reason: Optional[str] = Field(None, description="退场原因（死亡、完成使命等）")

    @classmethod
    def is_lc_serializable(cls) -> bool:
        return True

    @classmethod
    def get_lc_namespace(cls) -> List[str]:
        return ["src", "nodes", "common", "book_types"]


class CharacterRequirement(Serializable):
    """场景角色需求分析"""

    required_role_type: str = Field(..., description="需要的角色类型")
    required_abilities: List[str] = Field(default_factory=list, description="需要的能力特征")
    relationship_to_protagonist: str = Field(..., description="与主角的关系定位")
    scene_purpose: str = Field(..., description="在场景中的作用")
    tier: CharacterTier = Field(..., description="角色层级")
    priority: int = Field(default=1, description="优先级（1-5，5最高）")

    @classmethod
    def is_lc_serializable(cls) -> bool:
        return True

    @classmethod
    def get_lc_namespace(cls) -> List[str]:
        return ["src", "nodes", "common", "book_types"]


class CharacterSummary(Serializable):
    """角色简介信息 - 用于初始角色设计阶段，包含除详细描述外的所有基础信息"""

    name: str = Field(..., description="角色姓名")
    gender: str = Field(..., description="性别")
    role_type: str = Field(..., description="角色类型：主角、女主、配角、反派等")
    background: str = Field(..., description="背景故事")
    relationship_with_protagonist: str = Field(..., description="与主角的关系")
    appearance: str = Field(..., description="外观设定")
    abilities: str = Field(..., description="能力设定")
    brief_description: str = Field(..., description="三句话简要描述")
    key_interactions: List[str] = Field(..., description="与主角的关键互动")

    @classmethod
    def is_lc_serializable(cls) -> bool:
        """Return whether this model can be serialized by Langchain."""
        return True

    @classmethod
    def get_lc_namespace(cls) -> List[str]:
        """Get the namespace of the langchain object."""
        return ["src", "nodes", "common", "book_types"]

    def get_prompt_content(self) -> str:
        """获取角色简要内容，用于提示词中的角色摘要"""
        content_parts = []
        content_parts.append(f"**{self.name}（{self.role_type}）**")
        content_parts.append(f"**性别**: {self.gender}")
        content_parts.append(f"**背景**: {self.background}")
        content_parts.append(f"**与主角关系**: {self.relationship_with_protagonist}")
        content_parts.append(f"**简要设定**: {self.brief_description}")
        content_parts.append(f"**能力**: {self.abilities}")
        content_parts.append(f"**关键互动**: {', '.join(self.key_interactions)}")
        return "\n\n".join(content_parts)

    def get_full_content(self) -> str:
        """获取角色完整内容，用于文件保存"""
        content_parts = []
        content_parts.append(f"**{self.name}（{self.role_type}）**")
        content_parts.append(f"**性别**: {self.gender}")
        content_parts.append(f"**背景**: {self.background}")
        content_parts.append(f"**与主角关系**: {self.relationship_with_protagonist}")
        content_parts.append(f"**简要设定**: {self.brief_description}")
        content_parts.append(f"**外观**: {self.appearance}")
        content_parts.append(f"**能力**: {self.abilities}")
        content_parts.append(f"**关键互动**: {', '.join(self.key_interactions)}")
        return "\n\n".join(content_parts)


class CharacterDetail(Serializable):
    """角色详细信息 - 用于角色细化设计阶段，包含完整的角色信息"""

    name: str = Field(..., description="角色姓名")
    appearance: str = Field(..., description="外观设定")
    abilities: str = Field(..., description="能力设定")
    background: str = Field(..., description="背景故事")
    relationship_with_protagonist: str = Field(..., description="与主角的关系")
    key_interactions: List[str] = Field(..., description="与主角的关键互动")
    gender: str = Field(..., description="性别")
    role_type: str = Field(..., description="角色类型：主角、女主、配角、反派等")
    brief_description: str = Field(..., description="三句话简要描述")
    detailed_description: str = Field(..., description="详细段落描述")

    # 新增的动态角色管理字段
    tier: CharacterTier = Field(default="core", description="角色层级")
    lifecycle: Optional[CharacterLifecycle] = Field(None, description="角色生命周期")
    scene_appearances: List[str] = Field(default_factory=list, description="出现的场景ID列表")
    relationship_evolution: Dict[str, str] = Field(default_factory=dict, description="与其他角色关系的变化记录")
    development_notes: List[str] = Field(default_factory=list, description="角色发展记录")
    is_active: bool = Field(default=True, description="角色是否仍然活跃")

    @classmethod
    def is_lc_serializable(cls) -> bool:
        """Return whether this model can be serialized by Langchain."""
        return True

    @classmethod
    def get_lc_namespace(cls) -> List[str]:
        """Get the namespace of the langchain object."""
        return ["src", "nodes", "common", "book_types"]

    def get_full_content(self) -> str:
        """获取角色详情完整内容，用于文件保存"""
        content_parts = []
        content_parts.append(f"**{self.name}（{self.role_type}）**\n")
        content_parts.append(f"**性别**: {self.gender}\n")
        content_parts.append(f"**详细设定**: {self.detailed_description}\n")
        content_parts.append(f"**外观**: {self.appearance}\n")
        content_parts.append(f"**能力**: {self.abilities}\n")
        content_parts.append(f"**背景**: {self.background}\n")
        content_parts.append(f"**与主角关系**: {self.relationship_with_protagonist}\n")
        content_parts.append(f"**关键互动**: {', '.join(self.key_interactions)}\n")

        # 添加动态管理信息
        if self.tier != "core":
            content_parts.append(f"**角色层级**: {self.tier}\n")
        if self.lifecycle:
            content_parts.append(f"**首次出场**: 第{self.lifecycle.first_appearance_volume}卷\n")
            if self.lifecycle.active_volumes:
                content_parts.append(f"**活跃卷数**: {', '.join(map(str, self.lifecycle.active_volumes))}\n")
        if self.development_notes:
            content_parts.append(f"**发展记录**: {'; '.join(self.development_notes)}\n")

        return "\n\n".join(content_parts)

    def add_scene_appearance(self, scene_id: str) -> None:
        """添加场景出现记录"""
        if scene_id not in self.scene_appearances:
            self.scene_appearances.append(scene_id)

    def add_development_note(self, note: str) -> None:
        """添加角色发展记录"""
        self.development_notes.append(note)

    def update_relationship(self, character_name: str, new_relationship: str) -> None:
        """更新与其他角色的关系"""
        self.relationship_evolution[character_name] = new_relationship

    def is_available_for_volume(self, volume_number: int) -> bool:
        """检查角色在指定卷是否可用"""
        if not self.is_active:
            return False
        if self.lifecycle and self.lifecycle.exit_volume and volume_number > self.lifecycle.exit_volume:
            return False
        if self.lifecycle and volume_number < self.lifecycle.first_appearance_volume:
            return False
        return True


class CharacterSummaryCollection(Serializable):
    """角色简介集合"""

    characters: List[CharacterSummary] = Field(default_factory=list, description="角色简介列表")

    @classmethod
    def is_lc_serializable(cls) -> bool:
        """Return whether this model can be serialized by Langchain."""
        return True

    @classmethod
    def get_lc_namespace(cls) -> List[str]:
        """Get the namespace of the langchain object."""
        return ["src", "nodes", "common", "book_types"]

    def get_prompt_content(self, exclude_name: Optional[str] = None) -> str:
        """获取角色简介内容"""
        if not self.characters:
            return "暂无角色设定"

        content_parts = []

        for i, char in enumerate(self.characters, 1):
            if char.name != exclude_name:
                content_parts.append(f"## {i}. {char.get_prompt_content()}")
                content_parts.append("")  # 空行分隔

        return "\n\n".join(content_parts)

    def get_characters_content(self) -> str:
        """获取角色简介内容"""
        if not self.characters:
            return "暂无角色设定"

        content_parts = []
        content_parts.append("# 角色简介集\n")

        for i, char in enumerate(self.characters, 1):
            content_parts.append(f"## {i}. {char.get_full_content()}")
            content_parts.append("")  # 空行分隔

        return "\n\n".join(content_parts)

    def get_character_names(self) -> List[str]:
        """获取所有角色姓名列表"""
        return [char.name for char in self.characters]


class CharacterDetailCollection(Serializable):
    """角色详情集合"""

    characters: List[CharacterDetail] = Field(default_factory=list, description="角色详情列表")

    @classmethod
    def is_lc_serializable(cls) -> bool:
        """Return whether this model can be serialized by Langchain."""
        return True

    @classmethod
    def get_lc_namespace(cls) -> List[str]:
        """Get the namespace of the langchain object."""
        return ["src", "nodes", "common", "book_types"]

    def get_characters_content(self) -> str:
        """获取角色详情内容"""
        if not self.characters:
            return "暂无角色详情"

        content_parts = []
        content_parts.append("# 角色详情集\n")

        for i, char in enumerate(self.characters, 1):
            content_parts.append(f"## {i}. {char.get_full_content()}")
            content_parts.append("")  # 空行分隔

        return "\n\n".join(content_parts)

    def get_character_by_name(self, name: str) -> Optional[CharacterDetail]:
        """根据姓名获取角色详情"""
        for char in self.characters:
            if char.name == name:
                return char
        return None

    def get_characters_by_names(self, names: List[str]) -> List[CharacterDetail]:
        """根据姓名列表获取角色详情列表"""
        result = []
        for name in names:
            char = self.get_character_by_name(name)
            if char:
                result.append(char)
        return result

    def get_characters_by_tier(self, tier: CharacterTier) -> List[CharacterDetail]:
        """根据角色层级获取角色列表"""
        return [char for char in self.characters if char.tier == tier]

    def get_available_characters_for_volume(self, volume_number: int) -> List[CharacterDetail]:
        """获取在指定卷可用的角色列表"""
        return [char for char in self.characters if char.is_available_for_volume(volume_number)]

    def add_character(self, character: CharacterDetail) -> None:
        """添加新角色"""
        # 检查是否已存在同名角色
        existing = self.get_character_by_name(character.name)
        if existing:
            raise ValueError(f"角色 {character.name} 已存在")
        self.characters.append(character)

    def update_character_lifecycle(self, character_name: str, lifecycle: CharacterLifecycle) -> bool:
        """更新角色生命周期"""
        char = self.get_character_by_name(character_name)
        if char:
            char.lifecycle = lifecycle
            return True
        return False

    def get_core_characters_content(self) -> str:
        """获取核心角色内容（用于场景设计时的角色参考）"""
        core_characters = self.get_characters_by_tier("core")
        if not core_characters:
            return "暂无核心角色"

        content_parts = []
        content_parts.append("# 核心角色集\n")

        for i, char in enumerate(core_characters, 1):
            content_parts.append(f"## {i}. {char.get_full_content()}")
            content_parts.append("")  # 空行分隔

        return "\n\n".join(content_parts)


class Scene(Serializable):
    """单个场景信息"""

    scene_number: int = Field(..., description="场景编号")
    volume_number: int = Field(..., description="所属卷编号")
    location: str = Field(..., description="场景地点")
    featured_characters: List[str] = Field(default_factory=list, description="主要出场角色（仅包含已设定的角色）")
    core_events: List[str] = Field(default_factory=list, description="核心事件列表")
    scene_summary: str = Field(..., description="场景概要")
    conflicts: List[str] = Field(default_factory=list, description="场景的矛盾冲突")
    scene_purpose: str = Field(..., description="场景目的和意义")
    estimated_chapters: int = Field(default=1, description="预计章节数量")

    # 新增的角色需求分析字段
    character_requirements: List[CharacterRequirement] = Field(default_factory=list, description="场景角色需求列表")
    missing_characters: List[str] = Field(default_factory=list, description="缺失的角色类型")
    suggested_new_characters: List[str] = Field(default_factory=list, description="建议创建的新角色")

    @classmethod
    def is_lc_serializable(cls) -> bool:
        """Return whether this model can be serialized by Langchain."""
        return True

    @classmethod
    def get_lc_namespace(cls) -> List[str]:
        """Get the namespace of the langchain object."""
        return ["src", "nodes", "common", "book_types"]

    def get_scene_content(self) -> str:
        """获取场景内容的文本描述"""
        content_parts = []
        content_parts.append(f"## 场景{self.scene_number}：{self.location}")
        content_parts.append(f"**所属卷次**: 第{self.volume_number}卷")
        content_parts.append(f"**场景概要**: {self.scene_summary}")

        if self.featured_characters:
            content_parts.append(f"**主要出场角色**: {', '.join(self.featured_characters)}")

        if self.core_events:
            content_parts.append("**核心事件**:")
            for i, event in enumerate(self.core_events, 1):
                content_parts.append(f"  {i}. {event}")

        if self.conflicts:
            content_parts.append("**矛盾冲突**:")
            for i, conflict in enumerate(self.conflicts, 1):
                content_parts.append(f"  {i}. {conflict}")

        content_parts.append(f"**场景目的**: {self.scene_purpose}")
        content_parts.append(f"**预计章节**: {self.estimated_chapters}章")
        content_parts.append("")  # 空行分隔

        return "\n\n".join(content_parts)

    def add_character_requirement(self, requirement: CharacterRequirement) -> None:
        """添加角色需求"""
        self.character_requirements.append(requirement)

    def analyze_character_needs(self, available_characters: List[CharacterDetail]) -> Dict[str, Any]:
        """分析场景的角色需求，返回分析结果"""
        available_names = [char.name for char in available_characters]

        # 检查已指定的角色是否都可用
        missing_featured = [name for name in self.featured_characters if name not in available_names]

        # 分析场景需要什么类型的新角色
        analysis = {
            "scene_id": f"vol{self.volume_number}_scene{self.scene_number}",
            "missing_featured_characters": missing_featured,
            "available_characters": [char for char in available_characters if char.name in self.featured_characters],
            "character_gap_analysis": self._analyze_character_gaps(),
            "suggested_character_types": self._suggest_character_types(),
        }

        return analysis

    def _analyze_character_gaps(self) -> List[str]:
        """分析角色缺口"""
        gaps = []

        # 基于场景类型分析需要的角色
        if any("战斗" in event or "冲突" in event for event in self.core_events):
            gaps.append("战斗型角色")
        if any("谈判" in event or "交涉" in event for event in self.core_events):
            gaps.append("外交型角色")
        if any("探索" in event or "发现" in event for event in self.core_events):
            gaps.append("探索型角色")
        if any("反派" in conflict or "敌对" in conflict for conflict in self.conflicts):
            gaps.append("反派角色")

        return gaps

    def _suggest_character_types(self) -> List[str]:
        """建议需要创建的角色类型"""
        suggestions = []

        # 基于场景目的建议角色
        if "推进主线" in self.scene_purpose:
            suggestions.append("关键剧情角色")
        if "情感发展" in self.scene_purpose:
            suggestions.append("情感支撑角色")
        if "世界观展示" in self.scene_purpose:
            suggestions.append("世界观解说角色")

        return suggestions


class VolumeSceneDesign(Serializable):
    """卷场景设计"""

    volume_number: int = Field(..., description="卷编号")
    scenes: List[Scene] = Field(default_factory=list, description="场景列表")
    design_completed: bool = Field(default=False, description="设计是否完成")

    @classmethod
    def is_lc_serializable(cls) -> bool:
        """Return whether this model can be serialized by Langchain."""
        return True

    @classmethod
    def get_lc_namespace(cls) -> List[str]:
        """Get the namespace of the langchain object."""
        return ["src", "nodes", "common", "book_types"]

    def get_scenes_content(self) -> str:
        """获取所有场景内容"""
        if not self.scenes:
            return f"第{self.volume_number}卷暂无场景设计"

        content_parts = []
        content_parts.append(f"# 第{self.volume_number}卷场景清单\n")

        for scene in self.scenes:
            content_parts.append(scene.get_scene_content())

        return "\n\n".join(content_parts)

    def get_scene_by_number(self, scene_number: int) -> Optional[Scene]:
        """根据场景编号获取场景"""
        for scene in self.scenes:
            if scene.scene_number == scene_number:
                return scene
        return None


class BookSceneDesign(Serializable):
    """整书场景设计"""

    volume_scenes: List[VolumeSceneDesign] = Field(default_factory=list, description="各卷场景设计")

    @classmethod
    def is_lc_serializable(cls) -> bool:
        """Return whether this model can be serialized by Langchain."""
        return True

    @classmethod
    def get_lc_namespace(cls) -> List[str]:
        """Get the namespace of the langchain object."""
        return ["src", "nodes", "common", "book_types"]

    def is_all_volumes_completed(self) -> bool:
        """检查所有卷的场景设计是否完成"""
        return all(vs.design_completed for vs in self.volume_scenes)

    def need_scene_design_for_current_progress(
        self, current_chapter_number: int, chapters_per_volume: int = 100
    ) -> bool:
        """
        基于当前章节进度检查是否需要更多场景设计
        策略：仅将当前章节进度以及后续3个场景进行细化

        Args:
            current_chapter_number: 当前章节编号（从1开始）
            chapters_per_volume: 每卷的章节数量，默认100章

        Returns:
            bool: 是否需要更多场景设计
        """
        if current_chapter_number <= 0:
            return True

        # 计算当前章节所属的卷编号（从1开始）
        current_volume_number = (current_chapter_number - 1) // chapters_per_volume + 1

        # 获取当前卷的场景设计
        current_volume_scenes = self.get_volume_scenes(current_volume_number)
        if not current_volume_scenes or not current_volume_scenes.design_completed:
            return True

        # 检查是否有足够的后续场景
        # 从当前章节开始，向后查找3个场景
        found_scenes_count = 0

        # 从当前卷开始向后查找场景
        for vol_scenes in self.volume_scenes:
            if vol_scenes.volume_number >= current_volume_number and vol_scenes.design_completed:
                for scene in vol_scenes.scenes:
                    # 检查场景是否覆盖当前章节或后续章节
                    scene_start_chapter = (scene.volume_number - 1) * chapters_per_volume + 1
                    scene_end_chapter = scene_start_chapter + scene.estimated_chapters - 1

                    if scene_end_chapter >= current_chapter_number:
                        found_scenes_count += 1
                        if found_scenes_count >= 4:  # 当前场景+后续3个场景
                            return False

        return True

    def is_scene_ready_for_writing(self, current_chapter_number: int, chapters_per_volume: int = 100) -> bool:
        """
        检查场景设计是否足够支持当前章节的写作

        Args:
            current_chapter_number: 当前章节编号（从1开始）
            chapters_per_volume: 每卷的章节数量，默认100章

        Returns:
            bool: 场景设计是否足够支持写作
        """
        return not self.need_scene_design_for_current_progress(current_chapter_number, chapters_per_volume)

    def get_next_incomplete_volume_for_current_progress(
        self, current_chapter_number: int, chapters_per_volume: int = 100
    ) -> Optional[int]:
        """
        基于当前章节进度获取下一个需要场景设计的卷编号

        Args:
            current_chapter_number: 当前章节编号（从1开始）
            chapters_per_volume: 每卷的章节数量，默认100章

        Returns:
            Optional[int]: 下一个需要场景设计的卷编号，如果都已完成则返回None
        """
        if current_chapter_number <= 0:
            return self.get_next_incomplete_volume()

        # 计算当前章节所属的卷编号（从1开始）
        current_volume_number = (current_chapter_number - 1) // chapters_per_volume + 1

        # 从当前卷开始，向后查找未完成的卷（最多查找到当前卷+2卷的范围）
        max_search_volume = current_volume_number + 2

        for volume_number in range(current_volume_number, max_search_volume + 1):
            volume_scenes = self.get_volume_scenes(volume_number)
            if not volume_scenes or not volume_scenes.design_completed:
                return volume_number

        return None

    def get_next_incomplete_volume(self) -> Optional[int]:
        """获取下一个未完成场景设计的卷编号"""
        for vs in self.volume_scenes:
            if not vs.design_completed:
                return vs.volume_number
        return None

    def get_volume_scenes(self, volume_number: int) -> Optional[VolumeSceneDesign]:
        """获取指定卷的场景设计"""
        for vs in self.volume_scenes:
            if vs.volume_number == volume_number:
                return vs
        return None

    def get_scene_for_chapter(self, chapter_number: int, chapters_per_volume: int = 100) -> Optional[Scene]:
        """根据章节编号获取对应的场景"""
        if chapter_number <= 0:
            return None

        volume_number = (chapter_number - 1) // chapters_per_volume + 1
        volume_scenes = self.get_volume_scenes(volume_number)

        if not volume_scenes:
            return None

        # 简单分配：按章节顺序分配场景
        chapter_in_volume = ((chapter_number - 1) % chapters_per_volume) + 1

        current_chapter = 0
        for scene in volume_scenes.scenes:
            current_chapter += scene.estimated_chapters
            if chapter_in_volume <= current_chapter:
                return scene

        # 如果没找到合适的场景，返回最后一个场景
        return volume_scenes.scenes[-1] if volume_scenes.scenes else None

    def get_all_scenes_content(self) -> str:
        """获取所有场景内容"""
        if not self.volume_scenes:
            return "暂无场景设计"

        content_parts = []
        content_parts.append("# 整书场景清单\n")

        for volume_scene in self.volume_scenes:
            content_parts.append(volume_scene.get_scenes_content())
            content_parts.append("")  # 卷间空行

        return "\n\n".join(content_parts)


APP_SERIALIZABLE_MAPPINGS = {
    ("src", "nodes", "common", "book_types", "WriterPlanStep"): (
        "src",
        "nodes",
        "common",
        "book_types",
        "WriterPlanStep",
    ),
    ("src", "nodes", "common", "book_types", "WriterPlan"): (
        "src",
        "nodes",
        "common",
        "book_types",
        "WriterPlan",
    ),
    ("src", "nodes", "common", "book_types", "CharacterSummary"): (
        "src",
        "nodes",
        "common",
        "book_types",
        "CharacterSummary",
    ),
    ("src", "nodes", "common", "book_types", "CharacterDetail"): (
        "src",
        "nodes",
        "common",
        "book_types",
        "CharacterDetail",
    ),
    ("src", "nodes", "common", "book_types", "CharacterSummaryCollection"): (
        "src",
        "nodes",
        "common",
        "book_types",
        "CharacterSummaryCollection",
    ),
    ("src", "nodes", "common", "book_types", "CharacterDetailCollection"): (
        "src",
        "nodes",
        "common",
        "book_types",
        "CharacterDetailCollection",
    ),
    ("src", "nodes", "common", "book_types", "Scene"): (
        "src",
        "nodes",
        "common",
        "book_types",
        "Scene",
    ),
    ("src", "nodes", "common", "book_types", "VolumeSceneDesign"): (
        "src",
        "nodes",
        "common",
        "book_types",
        "VolumeSceneDesign",
    ),
    ("src", "nodes", "common", "book_types", "BookSceneDesign"): (
        "src",
        "nodes",
        "common",
        "book_types",
        "BookSceneDesign",
    ),
    ("src", "nodes", "common", "book_types", "CharacterLifecycle"): (
        "src",
        "nodes",
        "common",
        "book_types",
        "CharacterLifecycle",
    ),
    ("src", "nodes", "common", "book_types", "CharacterRequirement"): (
        "src",
        "nodes",
        "common",
        "book_types",
        "CharacterRequirement",
    ),
}

__all__ = [
    "WriterPlan",
    "WriterPlanStep",
    "WriterPlanSchema",
    "WriterPlanStepSchema",
    "ChapterOutline",
    "VolumeOutline",
    "BookDetail",
    "CharacterTier",
    "CharacterLifecycle",
    "CharacterRequirement",
    "CharacterSummary",
    "CharacterDetail",
    "CharacterSummaryCollection",
    "CharacterDetailCollection",
    "Scene",
    "VolumeSceneDesign",
    "BookSceneDesign",
    "APP_SERIALIZABLE_MAPPINGS",
]
