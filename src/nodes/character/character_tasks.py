from __future__ import annotations

from langgraph.func import task

from src.context.context_builder import ContextBuilder
from src.nodes.character.character_prompt import character_detailed_design_template
from src.nodes.common.book_types import CharacterDetail


@task
async def character_detail_task(payload: dict) -> dict:
    """
    LangGraph task：生成单个角色详情并落盘（副作用仅执行一次）。
    输入 payload 必须为 JSON 可序列化字典，包含：
      - refined_book_setting: str
      - plan_book_name: str
      - plan_book_description: str
      - character_name: str
      - target_summary: str
      - other_characters: str
      - book_name: str
      - book_id: str
      - configurable: dict  # 作为 llm_json 的 config.configurable
    返回：{"name": str, "detail": dict, "file_rel": str}
    """
    from src.nodes.common.node_kit import llm_json, write_text_under_book  # 延迟导入避免循环

    refined_book_setting: str = payload.get("refined_book_setting", "暂无")
    plan_book_name: str = payload.get("plan_book_name", payload.get("book_name", ""))
    plan_book_description: str = payload.get("plan_book_description", "")
    name: str = payload["character_name"]
    target_summary: str = payload.get("target_summary", "")
    other_characters: str = payload.get("other_characters", "")

    # 构造 prompt
    text = character_detailed_design_template.format(
        refined_book_setting=refined_book_setting,
        book_name=plan_book_name,
        book_description=plan_book_description or "",
        character_name=name,
        target_summary=target_summary,
        other_characters=other_characters,
    )
    cb = ContextBuilder()
    cb.header()
    cb.section("角色细化任务", text)
    cb.json_schema(CharacterDetail.model_json_schema())
    # budget 由上层通过 configurable.llm_params 控制，这里不做 tokens 预算裁剪
    messages = cb.build(max_tokens=None, policy="character")

    # 调用 LLM（结构化）
    config = {"configurable": payload.get("configurable", {})}
    detail: CharacterDetail = await llm_json("writer_character:detail", messages, CharacterDetail, config=config)

    # 兜底：name/tier
    try:
        if not getattr(detail, "name", None) or getattr(detail, "name") != name:
            setattr(detail, "name", name)
        if not getattr(detail, "tier", None):
            setattr(detail, "tier", "core")
    except Exception:
        pass

    # 单角色文件（以 summaries 中顺序命名的 idx 由上层提供困难；这里按名称写一份，聚合时会覆盖顺序版）
    book_name: str = payload.get("book_name", "")
    book_id: str = payload.get("book_id", "")
    rel = f"character/角色详情_{name}.md"
    try:
        write_text_under_book(book_name, book_id, rel, f"# {detail.get_full_content()}\n")
    except Exception:
        # 落盘失败不致命，聚合仍可继续
        pass

    return {"name": name, "detail": detail.model_dump(), "file_rel": rel}


__all__ = [
    "character_detail_task",
]
