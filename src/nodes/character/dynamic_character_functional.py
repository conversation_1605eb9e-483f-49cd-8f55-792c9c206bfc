from typing import Any, Dict, Literal, Optional

from langgraph.config import RunnableConfig
from langgraph.types import Command
from pydantic import BaseModel, Field

from src.context.context_builder import ContextBuilder
from src.nodes.common.book_types import BookSceneDesign, CharacterDetailCollection, CharacterRequirement, WriterPlan
from src.nodes.common.node_kit import build_resume_info, llm_json, write_text_under_book
from src.state import State


class DynamicCharacterAnalysisResult(BaseModel):
    """动态角色分析结果"""

    volume_number: int = Field(..., description="分析的卷编号")
    scene_analysis: list[Dict[str, Any]] = Field(default_factory=list, description="场景角色需求分析")
    character_gaps: list[str] = Field(default_factory=list, description="识别的角色缺口")
    suggested_characters: list[CharacterRequirement] = Field(default_factory=list, description="建议创建的角色")
    priority_characters: list[CharacterRequirement] = Field(default_factory=list, description="高优先级角色")


def _determine_target_volume(scene_design: Optional[BookSceneDesign]) -> int:
    if not scene_design or not getattr(scene_design, "volume_scenes", None):
        return 1
    for volume_scene in scene_design.volume_scenes:
        if volume_scene.design_completed and volume_scene.scenes:
            return volume_scene.volume_number
    return 1


def _build_messages(state: State, config: RunnableConfig, target_volume: int) -> list:
    refined_book_setting = state.get("refined_book_setting")
    character_details: CharacterDetailCollection | None = state.get("character_details")
    scene_design: BookSceneDesign | None = state.get("scene_design")

    # 获取目标卷的场景设计
    target_volume_scenes = None
    if scene_design is not None:
        for volume_scene in scene_design.volume_scenes:
            if volume_scene.volume_number == target_volume:
                target_volume_scenes = volume_scene
                break

    if target_volume_scenes is None or not target_volume_scenes.scenes:
        raise AssertionError(f"❌ 第{target_volume}卷的场景设计不存在或为空")

    cb = ContextBuilder()
    cb.header()
    cb.section(
        "角色需求分析任务",
        f"你是一个专业的小说角色需求分析师，需要分析第{target_volume}卷场景的角色需求。\n\n"
        f"分析第{target_volume}卷的所有场景，识别现有角色无法满足的需求，并建议需要创建的新角色。",
    )
    cb.section("设定集内容", refined_book_setting)
    cb.section("现有角色", character_details.get_characters_content() if character_details else "")
    cb.section("场景设计", target_volume_scenes.get_scenes_content())
    cb.section(
        "分析指南",
        "1. 场景角色匹配度：现有角色是否能满足场景需求\n2. 功能性缺口：缺少什么类型的角色功能\n"
        "3. 剧情推进需求：推进剧情需要什么样的角色\n4. 冲突制造需求：制造冲突需要什么样的角色\n\n"
        "角色分层建议：\n- 卷级角色（高优先级）\n- 场景角色（中优先级）\n- 功能角色（低优先级）",
    )
    cb.json_schema(DynamicCharacterAnalysisResult.model_json_schema())
    budget = config.get("configurable", {}).get("prompt_budget_tokens")
    return cb.build(max_tokens=budget, policy="dynamic_character")


async def dynamic_character_fn(state: State, config: RunnableConfig) -> Command[Literal["writer_scene"]]:
    # 前置：保持与旧实现语义一致
    plan: WriterPlan | None = state.get("writer_current_plan")
    if not plan:
        raise AssertionError("❌ 创作计划未完成")
    if state.get("character_details") is None:
        raise AssertionError("❌ 基础角色设计未完成")
    if state.get("scene_design") is None:
        raise AssertionError("❌ 场景设计未开始")

    target_volume = _determine_target_volume(state.get("scene_design"))
    messages = _build_messages(state, config, target_volume)

    parsed: DynamicCharacterAnalysisResult = await llm_json(
        "dynamic_character", messages, DynamicCharacterAnalysisResult, config=config
    )

    # 落盘
    file_rel = f"character/第{target_volume}卷角色需求分析.md"
    _ = write_text_under_book(state.get("book_name"), state.get("book_id"), file_rel, _format_analysis_content(parsed))

    # 人工审核已移除，直接进入后续
    next_node = "writer_scene"

    update = {f"character_analysis_vol_{target_volume}": parsed}
    if parsed.priority_characters:
        update["pending_character_creation"] = True
        update["pending_characters"] = parsed.priority_characters

    update["resume_info"] = build_resume_info(
        state,
        node="dynamic_character",
        next_node=next_node,
        summary=f"角色需求分析：第{target_volume}卷 -> {next_node}",
        target_volume=target_volume,
    )

    return Command(update=update, goto=next_node)


def _format_analysis_content(analysis: DynamicCharacterAnalysisResult) -> str:
    parts: list[str] = []
    parts.append(f"# 第{analysis.volume_number}卷角色需求分析\n")

    if analysis.scene_analysis:
        parts.append("## 场景角色需求分析\n")
        for i, scene_info in enumerate(analysis.scene_analysis, 1):
            parts.append(f"### 场景{i}")
            parts.append(f"**场景ID**: {scene_info.get('scene_id', 'N/A')}")
            parts.append(f"**现有角色匹配度**: {scene_info.get('character_match', 'N/A')}")
            gaps = scene_info.get("gaps", []) or []
            parts.append(f"**识别缺口**: {', '.join(gaps)}")
            parts.append("")

    if analysis.character_gaps:
        parts.append("## 角色缺口总结\n")
        for i, gap in enumerate(analysis.character_gaps, 1):
            parts.append(f"{i}. {gap}")
        parts.append("")

    if analysis.suggested_characters:
        parts.append("## 建议创建的角色\n")
        for i, char_req in enumerate(analysis.suggested_characters, 1):
            parts.append(f"### {i}. {char_req.required_role_type}")
            parts.append(f"**层级**: {char_req.tier.value}")
            parts.append(f"**优先级**: {char_req.priority}/5")
            parts.append(f"**与主角关系**: {char_req.relationship_to_protagonist}")
            parts.append(f"**场景作用**: {char_req.scene_purpose}")
            if getattr(char_req, "required_abilities", None):
                parts.append(f"**需要能力**: {', '.join(char_req.required_abilities)}")
            parts.append("")

    if analysis.priority_characters:
        parts.append("## 高优先级角色（建议立即创建）\n")
        for i, char_req in enumerate(analysis.priority_characters, 1):
            parts.append(f"### {i}. {char_req.required_role_type}")
            parts.append(f"**紧急程度**: 优先级 {char_req.priority}/5")
            parts.append(f"**作用**: {char_req.scene_purpose}")
            parts.append("")

    return "\n".join(parts)


__all__ = ["dynamic_character_fn", "DynamicCharacterAnalysisResult"]
