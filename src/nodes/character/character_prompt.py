# 角色设计相关的提示词模板

# 角色细化设定模板 - 为单个角色生成详细设定
character_detailed_design_template = """你是一个资深的玄幻爽文网络小说角色设计师，现在需要为指定角色进行详细设计。

# 任务说明
基于已有的角色简介，为指定角色撰写详细的设定，使角色更加立体和丰满。

# 设定集内容
{refined_book_setting}

# 书籍信息
- 书名: {book_name}
- 简介: {book_description}

# 其他角色简介
{other_characters}

# 目标角色
{target_summary}

# 细化要求

## 详细描述规范
为这个角色撰写一个段落（1500-2000字）的详细描述，包含：

1. **性格深度**: 详细展现角色的性格特点、内心世界、价值观念
2. **能力细节**: 具体描述角色的特殊能力、技能水平、战斗方式等
3. **背景丰富**: 补充角色的成长经历、重要转折点、形成现状的原因
4. **关系网络**: 详细说明与主角和其他角色的复杂关系和情感纠葛
5. **成长轨迹**: 预期角色在故事中的发展方向和变化

## 各属性要求
- **外观设定**: 详细的外貌描述，包括身材、容貌、穿着风格等
- **能力设定**: 具体的能力描述，符合世界观设定
- **背景故事**: 详细的成长经历和重要事件
- **与主角关系**: 明确且有发展潜力的关系定位
- **关键互动**: 具体的互动场景和情节点

## 写作要点
- 保持角色的一致性，与简介设定无冲突
- 增加角色的复杂性和矛盾性，避免完人设定
- 为角色设置成长空间和发展可能
- 体现角色的独特魅力和存在价值
- 确保符合世界观和故事背景
- 与其他角色形成良好的互补关系，避免功能重复或关系冲突


# 注意事项
- 详细描述要基于角色简介进行扩展，不可推翻原有设定
- 段落描述要有足够的信息量和深度
- 女性角色要避免过于刻板的设定，展现多元魅力
- 反派角色要有人性化的一面，增加故事深度
- 确保角色设定与世界观高度契合
- 主角需要更详尽的设定


请开始{character_name}的详细设计：
"""

# Demo章节模板（如果有示例章节）
character_demo_chapter_template = """
# 参考章节示例
以下是一个章节示例，请参考其中的角色描写风格和互动方式：

{demo_chapter}

请确保角色设定与示例章节的风格和调性保持一致。
"""

# 核心角色设计模板 - 专注于设计3-5个核心角色
core_character_design_template = """你是一个资深的玄幻爽文网络小说角色设计师，现在需要设计小说的核心角色阵容。

# 任务说明
基于已完成的设定集，设计本书的**核心角色**（3-5个）。这些是贯穿全书的主要角色，其他角色将在后续根据剧情需要动态添加。

# 设定集内容
<设定集内容>
{refined_book_setting}
</设定集内容>

# 书籍信息
- 书名: {book_name}
- 简介: {book_description}

# 核心角色设计原则

## 角色定位
**核心角色**是小说的灵魂，包括：
- **主角**：故事的绝对中心
- **女主**：主要的情感线角色（1-2个）
- **核心配角**：推动主线剧情的关键角色（1-2个）

## 设计规范
1. **角色数量**: 严格控制在3-5个核心角色
2. **角色重要性**: 每个角色都必须在整个故事中有重要作用
3. **角色关系**: 核心角色间要有复杂的关系网络
4. **成长空间**: 每个角色都要有明确的成长轨迹

## 角色性别倾向原则
- **主角**: 必须是男性
- **女主**: 1-2个重要女性角色
- **核心配角**: 根据剧情需要，但要平衡

## 角色属性要求
每个核心角色需要包含以下属性：
- **姓名**: 符合世界观设定的姓名
- **性别**: 符合角色倾向原则
- **角色类型**: 主角、女主、核心配角等
- **外观设定**: 详细的外貌描述
- **能力设定**: 符合世界观的核心能力
- **背景**: 详细的出身和过往经历
- **与主角的关系**: 明确与主角的关系定位
- **关键互动**: 预期与主角的重要互动场景
- **角色弧线**: 在整个故事中的发展轨迹

# 重要说明
- 这些核心角色将贯穿整个故事
- 其他反派、配角等将在后续根据具体卷和场景需要动态创建
- 专注于质量而非数量，每个角色都要有深度和独特性
- 确保角色间有足够的化学反应和冲突潜力

请开始核心角色设计：
"""

__all__ = [
    "core_character_design_template",
    "character_detailed_design_template",
    "character_demo_chapter_template",
]
