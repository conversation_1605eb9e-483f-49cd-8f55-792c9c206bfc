# QFlowAgent v3.0 规划与路线图（Functional API 改造）

> 概述 v3.0 的目标、TODO、DONE，并以多张图展示：整体架构、Functional 流程、人审中断、状态分层与“流写→分章→下一章”增量循环。
> 设计对齐 LangGraph Functional API（持久化、内存、人审、流式），参考官方指南（含“View thread state (checkpoint)”与“Decouple return value from saved value”）。

---

## 目标（Goals）
- 完全迁移至 Functional API（`@entrypoint` + `@task`），保留持久化、可恢复与流式能力。
- 统一持久化：Postgres Checkpointer + AsyncPostgresStore（命名空间 `writer_app_v2`）。
- 人机协作：已移除人工审核与 interrupt 交互，统一自动推进。
- 上下文分层与稳健性：统一 `State / Configurable / RuntimeContext` 边界；节点最小化业务逻辑。
- 增量生产：卷纲/场景按需增量；“流写→分章→下一章判定→补场景/下卷”闭环。
- 可观察性：ResumeInfo 摘要稳定输出；Checkpoint 列表/树易读可查。

---

## 架构总览（Functional + 持久化）
```mermaid
flowchart LR
  U["用户/CLI"] --> EP["@entrypoint qflow_workflow"]
  EP --> N["函数式节点\n(bs_planner → ... → prepare_next_chapter)"]
  N --> WS["Workspace 文件"]
  N --> STORE["AsyncPostgresStore (pgvector)"]
  N --> CKP["Postgres Checkpointer"]
  CKP <-- 恢复/回放 --> EP
```

---

## Functional 主循环（状态推进）
```mermaid
flowchart TD
  I["inputs/messages/start_at"] --> D["决策 next_node"]
  D -->|bs_planner| P[bs_planner_fn]
  D -->|bs_steps| S[bs_steps_fn]
  D -->|bs_refine| R[bs_refine_fn]
  D -->|writer_character| C[writer_character_fn]
  D -->|writer_outline| O[writer_outline_fn]
  D -->|writer_scene| SC[writer_scene_fn]
  D -->|stream_write| W[stream_write_fn]
  D -->|chapter_segment| G[chapter_segment_fn]
  D -->|prepare_next_chapter| N[prepare_next_chapter_fn]
  P --> U1["Command(update,goto)"]
  S --> U1
  R --> U1
  C --> U1
  O --> U1
  SC --> U1
  W --> U1
  G --> U1
  N --> U1
  U1 --> D
```

---

## 变量分层（State / Configurable / Runtime）
```mermaid
graph LR
  subgraph Configurable
    k1[thread_id / book_name / prompt_budget_tokens]
    k2[write_chapters_cnt]
  end
  subgraph State
    s1[writer_current_plan / refined_book_setting]
    s2[book_detail / scene_design / character_*]
    s3[resume_info / skip_counters / writing_state]
  end
  subgraph RuntimeContext
    r1[DB clients / logger / cache]
  end
  k1 --> EP[@entrypoint]
  k2 --> EP
  EP --> s1
  EP --> s2
  EP --> s3
  EP --- r1
```

---

## 人审中断（已移除）

---

## 增量写作闭环（流写→分章→下一章）
```mermaid
flowchart TD
  SW["stream_write: 追加缓冲"] --> J{缓冲≥阈值? 或 达自循环上限?}
  J -- 否 --> SW
  J -- 是 --> SEG["chapter_segment: 智能分章"]
  SEG --> PNC["prepare_next_chapter: 摘要 + 判定"]
  PNC -->|need_switch_scene? 是| WSCR[writer_scene: 补场景/下卷]
  PNC -->|否| SW
  WSCR --> SW
```

---

## 模式抽象（对齐 0_idea）
```mermaid
flowchart LR
  subgraph Planner 模式
    A[planner] --> B[steps]
    B -->|loop| B
    B --> C[refine]
    C -->|loop until done| C
    C --> M[merge]
  end
  subgraph 并发模式
    L[list] --> G[gen]
    G -->|foreach + @task| G
    G --> J[merge]
  end
  subgraph 流式写作
    SW2[stream] --> SEG2[segment]
    SEG2 --> DEC2[decide next]
    DEC2 --> SW2
  end
```

---

## 里程碑（DONE）
- Functional 入口：`src/functional/entrypoint.py` `@entrypoint` + Postgres Checkpointer，统一推进节点并保存完整 `State`；返回值与保存值解耦。
- 运行切换：`apps/main.py` 优先走 Functional 流；旧 Graph 路径暂保留（将清理）。
- 节点兼容：`bs_steps_functional` 的 `runtime` 形参改为可选（Functional 可直接调用）。
- 存储兜底：`WriterStoreManager` 在非图上下文可懒加载本地 `AsyncPostgresStore`，保障入库。
- 事件兼容：`apps/event_handler.py` 适配对象存在 `stream/astream` 两种接口（为 Functional 事件做准备）。

---

## 任务清单（TODO）
- Functional 事件流
  - 在 Functional `.stream` 上输出 `updates/debug`（无人工审核）。
  - 将现有 CLI 事件映射迁移至 Functional 运行对象（不再依赖 Graph 编译器）。
- 人审开启策略
  - 默认按 CLI 开关配置，支持 `/skip <domain> [n]` 跨轮策略；更新 `state.skip_counters`。
- 清理旧路径
  - 移除 `src/graph/graph.py` 及其引用；文档中统一为 Functional 路径与命令。
- 单测与文档
  - 更新 unitest 覆盖（Functional 事件 / 中断）与 README 运行说明。
- 性能与稳健
  - 并发任务可观测性增强（任务 start/end 事件）；失败重试统计。

---

## 运行与恢复（现状）
- 新建或继续：`uv run apps/main.py`
- 从最新 checkpoint 恢复：`uv run apps/main.py --checkpoint-id latest`

---

## 备注
- Functional 入口对齐官方指南（持久化、state 恢复、返回值与保存值解耦），并保持与 v2 数据层约定一致。
- 清理旧 Graph 路径后，本文件会同步更新“运行与恢复”章节。


