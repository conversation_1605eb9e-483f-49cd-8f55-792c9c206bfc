import re

from src.common.utils.time_utils import get_db_timestamp, get_prompt_timestamp, get_str_timestamp


def test_get_db_timestamp_format():
    ts = get_db_timestamp()
    assert isinstance(ts, str)
    assert re.match(r"^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{6}$", ts)


def test_get_str_timestamp_format():
    ts = get_str_timestamp()
    assert isinstance(ts, str)
    assert re.match(r"^\d{8}_\d{6}_\d{6}$", ts)


async def test_async_example():
    assert 1 + 1 == 2
