import asyncio
import logging
import os
from pathlib import Path

from dotenv import load_dotenv
from langchain_community.tools.file_management.read import ReadFileTool
from langchain_core.messages.utils import count_tokens_approximately, trim_messages
from langchain_core.vectorstores import InMemoryVectorStore
from langchain_postgres import PGVector
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langgraph.checkpoint.postgres.aio import AsyncPostgresSaver
from langgraph.store.memory import InMemoryStore
from langgraph.store.postgres.aio import AsyncPostgresStore


if __name__ == "__main__":
    import sys

    sys.path.append(str(Path(__file__).parent.parent.parent))

from src.common.llms import get_default_embedding_model
from src.common.utils.path_utils import get_workspace_root
from src.init_log import init_log
from src.memory.store_utils import *

DB_URI = "postgresql://postgres:postgres@localhost:5432/qflow?sslmode=disable"

from langchain_community.embeddings import DashScopeEmbeddings
from langchain_core.documents import Document
from langchain_openai import OpenAIEmbeddings

load_dotenv(dotenv_path="src/common/models/.env")
init_log()


async def query_test():
    try:
        async with get_store_and_checkpointer() as (store, checkpointer):

            # 2. 测试语义搜索
            print("\n🔍 测试语义搜索功能")
            queries = [
                "主角的身份和血脉",
                "神界和凡界的区别",
                "修炼境界如何提升",
                "时空之道的掌握方法",
                "世界的力量体系",
            ]

            for query in queries:
                print("start" + "#" * 100)
                print(f"query: {query}")
                search_results = await store_asearch(
                    store,
                    ("writer_app_v2", "5fae047c-eeb6-4618-a83f-18830bcc2b9f"),
                    query=query,
                    limit=3,
                )
                for search_result in search_results:
                    print(f"search_result: {search_result}")
                    assert search_result.score != None
                    print("-" * 100)
                print("end" + "#" * 100)
    except Exception as e:
        logging.error(f"Error in store_test: {e}", exc_info=True)
        raise


if __name__ == "__main__":
    asyncio.run(query_test())
    pass
