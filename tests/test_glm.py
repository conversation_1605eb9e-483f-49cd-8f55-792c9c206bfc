import logging
import os

from dotenv import load_dotenv
from langchain.schema import HumanMessage, SystemMessage
from langchain_openai import ChatOpenAI

from src.common.llms import get_llm_by_model_name

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建LLM实例
llm = ChatOpenAI(
    temperature=0.7,
    model="glm-4.5",
    openai_api_key=os.getenv("GLM_API_KEY"),
    openai_api_base=os.getenv("GLM_BASE_URL"),
)

# 创建消息
messages = [
    SystemMessage(content="你是一个有用的AI助手"),
    HumanMessage(content="请介绍一下人工智能的发展历程"),
]

glm_4p5_air = "glm-4.5-air"  # glm的官方api jsonmode有问题
glm_4p5 = "glm-4.5"  # glm的官方api jsonmode有问题
glm_4p5_ppio = "glm-4.5-ppio"  # ppio的封装 jsonmode没问题 (会不会是think开关的问题)


llm = get_llm_by_model_name(glm_4p5_ppio, params={"extra_body": {"enable_thinking": False}})

# 调用模型
for chunk in llm.stream(
    messages,
    # config={
    #     "configurable": {
    #         "extra_body": {
    #             "thinking": {
    #                 "type": "disabled",  # enabled disabled
    #             },
    #             # "enable_thinking": False,
    #         },
    #     },
    # },
):
    print(chunk.content, end="", flush=True)
