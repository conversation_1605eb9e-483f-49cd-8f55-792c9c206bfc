from typing import Any, Callable, Dict, Optional

from src.common.utils.debug_utils import format_langgraph_debug_line

# 人工审核已移除


class EventHandler:
    def __init__(
        self,
        *,
        graph: Any,
        config: Dict[str, Any],
        input_func: Callable[[str], str] = input,
    ) -> None:
        self.graph = graph
        self.config = config
        self.input_func = input_func

        cfg = config.get("configurable") or {}
        try:
            self.preview_len = int(cfg.get("max_preview_len", 400))
        except (ValueError, TypeError):
            self.preview_len = 400

        self._start_times: Dict[str, float] = {}

        # 取消在 app 层维护友好映射，保持对 langgraph 事件的最少理解

    # -------------------- 工具方法 --------------------

    # 移除友好名称方法

    def _pretty_event_header(self, title: str) -> None:
        """打印格式化的事件标题"""
        print(f"\n{title}")
        print("=" * len(title))

    def _print_kv(self, label: str, data: Dict[str, Any], max_value_len: Optional[int] = None) -> None:
        """安全地打印键值对，限制值的长度"""
        try:
            limit = self.preview_len if max_value_len is None else max_value_len
            safe = {k: (str(v)[:limit] + ("..." if len(str(v)) > limit else "")) for k, v in data.items()}
            print(f"{label}:", safe)
        except Exception:
            # 静默失败，避免影响主流程
            pass

    def _safe_preview_text(self, text: Any, limit: int = 200) -> str:
        """安全地截取文本预览"""
        try:
            s = str(text)
            return s[:limit] + ("..." if len(s) > limit else "")
        except Exception:
            return "-"

    # 删除内建格式化逻辑，转而使用 src 公共工具（保持 app 侧轻量）

    # 审核相关已移除
    # -------------------- 核心逻辑 --------------------

    async def run(self, input_messages: Any) -> None:
        astream = getattr(self.graph, "astream", None)
        use_astream = callable(astream)
        if use_astream:
            try:
                agen = self.graph.astream(
                    input_messages, self.config, stream_mode=["updates", "debug"], durability="sync"
                )
            except TypeError:
                # Functional API .astream 不支持 durability 形参时降级
                agen = self.graph.astream(input_messages, self.config, stream_mode=["updates", "debug"])
        else:
            stream = getattr(self.graph, "stream", None)
            if not callable(stream):
                raise ValueError("Object does not support stream/astream")
            agen = self.graph.stream(input_messages, self.config, stream_mode=["updates", "debug"])  # type: ignore

        async for chunk in agen:
            # 兼容 tuple 形式 (event_type, event_value) 与直接 update/debug 事件
            if isinstance(chunk, tuple) and len(chunk) == 2:
                event_type, event_value = chunk
                if event_type == "updates":
                    await self._handle_regular_chunk(event_value)
                elif event_type == "debug":
                    await self._handle_debug_chunk(event_value)
            else:
                await self._handle_regular_chunk(chunk)

    async def _handle_regular_chunk(self, chunk: Any) -> None:
        """处理常规更新块"""
        if isinstance(chunk, dict):
            keys = list(chunk.keys())
            if keys:
                # print("更新字段:", keys)
                pass
        else:
            text = str(chunk)
            lim = min(500, self.preview_len)
            print(text[:lim] + ("..." if len(text) > lim else ""))

    # 审核相关已移除
    async def _handle_debug_chunk(self, event_value: Any) -> None:
        # 统一由格式化器生成单行输出，避免制表符造成的错位
        try:
            print(format_langgraph_debug_line(event_value, preview_len=self.preview_len))
        except Exception:
            # 兜底：出现异常时回退到简单打印，保证不中断主流程
            debug_event_type = event_value.get("type", "-") if isinstance(event_value, dict) else "-"
            step = event_value.get("step", "-") if isinstance(event_value, dict) else "-"
            print(f"step.{step}(type={debug_event_type})")
