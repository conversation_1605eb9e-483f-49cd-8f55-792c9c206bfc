#!/usr/bin/env python3
"""
🔍 向量语义检索工具

功能：
- 按书ID查询设定/场景向量
- 支持自定义查询语句
- 支持结果数量限制
- 显示相似度分数

使用示例：
  uv run apps/vector_query.py --book-id "5fae047c-eeb6-4618-a83f-18830bcc2b9f"
  uv run apps/vector_query.py --book-id "5fae047c-eeb6-4618-a83f-18830bcc2b9f" --query "主角的身份和血脉"
  uv run apps/vector_query.py --book-id "5fae047c-eeb6-4618-a83f-18830bcc2b9f" --limit 5
"""

import argparse
import asyncio
import logging
import sys
from pathlib import Path
from typing import List, Optional

from dotenv import load_dotenv

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from src.init_log import init_log
from src.memory.store_utils import get_store_and_checkpointer, store_asearch

# 加载环境变量
load_dotenv(dotenv_path="src/common/models/.env")
init_log()

# 默认查询列表
DEFAULT_QUERIES = [
    "主角的身份和血脉",
    "神界和凡界的区别",
    "修炼境界如何提升",
    "时空之道的掌握方法",
    "世界的力量体系",
    "主要角色关系",
    "世界观设定",
    "魔法体系",
    "战斗系统",
    "剧情主线",
]


async def query_vector_store(
    book_id: str, queries: Optional[List[str]] = None, limit: int = 3, namespace_prefix: str = "writer_app_v2"
) -> None:
    """
    查询向量存储

    Args:
        book_id: 书籍ID
        queries: 查询列表，如果为None则使用默认查询
        limit: 每个查询返回的结果数量
        namespace_prefix: 命名空间前缀
    """
    if queries is None:
        queries = DEFAULT_QUERIES

    try:
        async with get_store_and_checkpointer() as (store, _):
            print("🔍 开始向量语义检索")
            print(f"📖 书籍ID: {book_id}")
            print(f"🎯 命名空间: {namespace_prefix}")
            print(f"📊 结果限制: {limit}")
            print("=" * 80)

            namespace = (namespace_prefix, book_id)

            for i, query in enumerate(queries, 1):
                print(f"\n🔎 查询 {i}/{len(queries)}: {query}")
                print("─" * 60)

                try:
                    search_results = await store_asearch(
                        store,
                        namespace,
                        query=query,
                        limit=limit,
                    )

                    if not search_results:
                        print("   ❌ 未找到相关结果")
                        continue

                    print(f"   ✅ 找到 {len(search_results)} 个结果:")

                    for j, result in enumerate(search_results, 1):
                        score = getattr(result, "score", None)
                        value = getattr(result, "value", {})
                        content = value.get("content", "")
                        metadata = value.get("metadata", {})

                        print(f"\n   📄 结果 {j}:")
                        print(f"      相似度: {score:.4f}" if score is not None else "      相似度: N/A")

                        # 显示元数据
                        if metadata:
                            print(f"      元数据: {metadata}")

                        # 显示内容预览（限制长度）
                        if content:
                            preview = content[:200] + "..." if len(content) > 200 else content
                            print(f"      内容预览: {preview}")

                        print("      " + "─" * 40)

                except Exception as e:
                    print(f"   ❌ 查询失败: {str(e)}")
                    logging.error(f"查询失败: {query} - {e}", exc_info=True)

            print("\n" + "=" * 80)
            print("🎉 向量检索完成")

    except Exception as e:
        logging.error(f"向量检索出错: {e}", exc_info=True)
        print(f"❌ 向量检索出错: {e}")
        raise


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="向量语义检索工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  %(prog)s --book-id "5fae047c-eeb6-4618-a83f-18830bcc2b9f"
  %(prog)s --book-id "5fae047c-eeb6-4618-a83f-18830bcc2b9f" --query "主角的身份"
  %(prog)s --book-id "5fae047c-eeb6-4618-a83f-18830bcc2b9f" --limit 5 --namespace "custom_app"
        """,
    )

    parser.add_argument("--book-id", required=True, help="书籍ID（必需）")

    parser.add_argument("--query", action="append", help="自定义查询语句（可多次使用）。如果不指定，将使用默认查询列表")

    parser.add_argument("--limit", type=int, default=3, help="每个查询返回的结果数量（默认: 3）")

    parser.add_argument("--namespace", default="writer_app_v2", help="命名空间前缀（默认: writer_app_v2）")

    parser.add_argument("--list-default-queries", action="store_true", help="显示默认查询列表并退出")

    return parser.parse_args()


async def main():
    """主函数"""
    args = parse_args()

    # 显示默认查询列表
    if args.list_default_queries:
        print("📋 默认查询列表:")
        for i, query in enumerate(DEFAULT_QUERIES, 1):
            print(f"  {i:2d}. {query}")
        return

    # 检查环境变量
    import os

    if not os.getenv("ALI_API_KEY"):
        print("❌ 请设置 ALI_API_KEY 环境变量（用于DashScope Embedding）")
        print("   配置文件位置: src/common/models/.env")
        return

    # 准备查询列表
    queries = args.query if args.query else None

    print("🚀 启动向量语义检索工具")
    print("=" * 80)

    if queries:
        print(f"📝 使用自定义查询 ({len(queries)} 个):")
        for i, query in enumerate(queries, 1):
            print(f"  {i}. {query}")
    else:
        print(f"📝 使用默认查询列表 ({len(DEFAULT_QUERIES)} 个)")
        print("   使用 --list-default-queries 查看完整列表")

    try:
        await query_vector_store(
            book_id=args.book_id, queries=queries, limit=args.limit, namespace_prefix=args.namespace
        )
    except KeyboardInterrupt:
        print("\n👋 用户中断，程序退出")
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
