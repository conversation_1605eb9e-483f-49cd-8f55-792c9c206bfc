-- Active: 1747581652851@@127.0.0.1@5432@qflow



SELECT tablename FROM pg_tables WHERE schemaname = 'public';
# store_migrations
# store
# checkpoint_migrations
# checkpoints
# checkpoint_blobs
# checkpoint_writes

select * from store order by created_at desc

select * from store_vectors order by created_at desc

select * from checkpoints;

select checkpoint::json->'ts' as ts,* from checkpoints; where thread_id = 'new_book.py_0f4c7ff3-b4e5-4a3e-a333-e9ff4d6a62d3'


SELECT '{"bar": "baz", "balance": 7.77, "active": false}'::json->'bar'


select * from store where prefix like 'writer_app_v2%test_book_id%'

delete from store where prefix like 'writer_app_v2%test_book_id%'



ALTER DATABASE qflow SET timezone TO 'Asia/Shanghai';
