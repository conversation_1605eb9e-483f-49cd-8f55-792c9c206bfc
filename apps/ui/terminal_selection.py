from __future__ import annotations

from typing import Any, Dict, List, Optional

from src.checkpoint import BookGroupSummary, CheckpointManager, CheckpointSummary
from src.common.utils.time_utils import format_human_datetime


def _format_book_line(index: int, book: BookGroupSummary) -> str:
    created = format_human_datetime(book.created_ts)
    updated = format_human_datetime(book.updated_ts)
    name = book.book_name or "(未命名)"
    cp_cnt = 0
    try:
        meta = getattr(book, "meta", None) or {}
        if isinstance(meta, dict):
            cp_cnt = int(meta.get("checkpoint_count") or 0)
    except Exception:
        cp_cnt = 0
    return f"{index}. 【{name}】{book.book_id} 创建时间：{created}，更新时间：{updated}，检查点：{cp_cnt} 个"


def _summary(meta_obj: Dict[str, Any]) -> str:
    if not isinstance(meta_obj, dict):
        return "(无摘要)"
    return meta_obj.get("summary") or (
        (meta_obj.get("node") or "")
        + (f"[{meta_obj.get('stage')}]" if meta_obj.get("stage") else "")
        + (f" -> {meta_obj.get('next')}" if meta_obj.get("next") else "")
    )


def _meta_brief(meta_obj: Dict[str, Any]) -> str:
    if not isinstance(meta_obj, dict):
        return ""
    keys = [
        "node",
        "stage",
        "next",
        "type",
        "step_title",
        "step_index",
        "is_last_step",
        "target",
        "target_volume",
        "current_chapter",
        "chapter_index",
        "next_chapter",
        "buffer_len",
    ]
    pairs: List[str] = []
    for k in keys:
        v = meta_obj.get(k)
        if v is not None and v != "":
            pairs.append(f"{k}={v}")
    return (" | " + ", ".join(pairs)) if pairs else ""


async def _prompt_select_checkpoint(
    mgr: CheckpointManager, book_id: str, limit: int = 10
) -> Optional[CheckpointSummary]:
    # 使用线性列表，按时间新→旧，限制最近 N 条
    cp_list = await mgr.list_checkpoints(book_id, limit=limit)
    if not cp_list:
        print("\n该书暂无可用的历史 checkpoint，将从最新状态继续...\n")
        return None

    print("\n可选的历史 checkpoint（最近，新→旧）：")
    idx_map: Dict[int, CheckpointSummary] = {}
    for i, c in enumerate(cp_list, start=1):
        idx_map[i] = c
        ts_h = format_human_datetime(c.ts)
        meta = getattr(c, "metadata", {}) or {}
        print(f"{i}. {c.checkpoint_id} @ {ts_h} (thread: {c.thread_id}) | {_summary(meta)}{_meta_brief(meta)}")

    print("0. 使用最新状态（不回退）")
    while True:
        try:
            choice = input(f"\n请选择 checkpoint (0-{len(idx_map)}): ").strip()
            if choice == "0":
                return None
            idx = int(choice)
            if 1 <= idx <= len(idx_map):
                return idx_map[idx]
            print(f"无效选择，请输入 0-{len(idx_map)} 之间的数字")
        except ValueError as e:
            print(f"请输入有效的数字, 当前输入: {choice}, 错误信息: {e}")
        except KeyboardInterrupt:
            print("\n用户取消，默认使用最新状态...")
            return None


def _prompt_select_branch(group: BookGroupSummary) -> str:
    if len(group.thread_ids) <= 1:
        return group.representative_thread_id
    print("\n检测到该书籍存在多个线程（分支）：")
    for i, tid in enumerate(group.thread_ids, start=1):
        flag = "（代表）" if tid == group.representative_thread_id else ""
        print(f"{i}. {tid} {flag}")
    print("0. 使用代表线程（最新）")
    while True:
        try:
            choice = input(f"\n请选择分支 (0-{len(group.thread_ids)}): ").strip()
            if choice == "0":
                return group.representative_thread_id
            idx = int(choice)
            if 1 <= idx <= len(group.thread_ids):
                return group.thread_ids[idx - 1]
            print(f"无效选择，请输入 0-{len(group.thread_ids)} 之间的数字")
        except ValueError as e:
            print(f"请输入有效的数字, 当前输入: {choice}, 错误信息: {e}")
        except KeyboardInterrupt:
            print("\n用户取消，默认使用代表线程...")
            return group.representative_thread_id


async def prompt_book_action(mgr: CheckpointManager) -> Dict[str, Any]:
    books = await mgr.list_books()
    if not books:
        print("\n📚 暂无进行中的小说项目。\n")
        return {"action": "empty"}

    while True:
        print("\n" + "=" * 80)
        print("📚 发现以下进行中的小说项目:")
        print("=" * 80)
        for i, b in enumerate(books, start=1):
            print(_format_book_line(i, b))
        print("\n0. 创建新的小说项目")
        print("D. 删除小说项目")

        try:
            choice = input(f"\n请输入选择 (0-{len(books)} / D): ").strip().lower()
        except KeyboardInterrupt:
            print("\n用户取消，默认新建项目...")
            return {"action": "new"}

        if choice == "0":
            return {"action": "new"}

        if choice == "d":
            print("\n请选择要删除的项目：")
            for i, b in enumerate(books, start=1):
                print(_format_book_line(i, b))
            print("0. 取消删除")
            try:
                del_choice = input(f"\n请输入选择 (0-{len(books)}): ").strip()
            except KeyboardInterrupt:
                print("\n已取消删除")
                continue
            if del_choice == "0":
                print("已取消删除")
                continue
            try:
                idx = int(del_choice)
                if not (1 <= idx <= len(books)):
                    print("无效选择")
                    continue
            except ValueError:
                print("请输入有效数字")
                continue
            target = books[idx - 1]
            confirm = (
                input(
                    f"\n确认删除【{target.book_name or '(未命名)'}】(book_id={target.book_id}) 的全部数据？此操作不可恢复！(y/N): "
                )
                .strip()
                .lower()
            )
            if confirm != "y":
                print("已取消删除")
                continue
            return {"action": "delete", "book_id": target.book_id, "book_name": target.book_name}

        # 恢复流程
        try:
            idx = int(choice)
            if not (1 <= idx <= len(books)):
                print(f"无效选择，请输入 0-{len(books)} 之间的数字或 D")
                continue
        except ValueError as e:
            print(f"请输入有效的数字或 D, 当前输入: {choice}, 错误信息: {e}")
            continue

        selected = books[idx - 1]
        chosen_cp = await _prompt_select_checkpoint(mgr, selected.book_id, limit=10)
        if chosen_cp is not None:
            return {
                "action": "resume",
                "book_id": selected.book_id,
                "book_name": selected.book_name,
                "thread_id": chosen_cp.thread_id,
                "checkpoint_id": chosen_cp.checkpoint_id,
            }

        # 不回退：选择线程分支
        thread_id = _prompt_select_branch(selected)
        return {
            "action": "resume",
            "book_id": selected.book_id,
            "book_name": selected.book_name,
            "thread_id": thread_id,
        }
