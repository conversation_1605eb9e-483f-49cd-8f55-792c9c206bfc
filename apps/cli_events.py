from typing import Any, Callable, Dict

from event_handler import EventHandler


async def stream_graph_events(
    graph: Any,
    initial_input: Dict[str, Any],
    config: Dict[str, Any],
    *,
    input_func: Callable[[str], str] = input,
) -> None:
    """
    统一的事件流启动器（Functional Entrypoint）。
    - 固定使用 .astream(..., stream_mode=["updates","debug"])。
    - 已移除人工审核交互，事件仅用于输出调试与更新。
    """
    handler = EventHandler(graph=graph, config=config, input_func=input_func)
    await handler.run(initial_input)
