
模型回退功能、动态选择模型
https://langchain-ai.github.io/langgraph/agents/models/#dynamic-model-selection
> 请评估本项目的模型回退功能，能否使用 @langgraph 官方的功能


需要新增限速器功能
> https://langchain-ai.github.io/langgraph/agents/models/#use-the-built-in-rate-limiter


新增一个runtime类，用于保存运行时变量, 这样就不需要什么都通过State传递了
> https://langchain-ai.github.io/langgraph/concepts/low_level/?h=runtime#runtime-context


使用官方的token统计回调
> https://python.langchain.com/docs/how_to/chat_token_usage_tracking/#tracking-token-usage-through-configuration

trustcall优化json生成成功率
> https://github.com/hinthornw/trustcall

langmem 管理长期记忆？
> https://langchain-ai.github.io/langmem/background_quickstart/

fast api + terminal

qwwe_code
https://github.com/QwenLM/qwen-code
pnpm install -g @qwen-code/qwen-code
qwen --version
qwen 
/compress - 将对话历史压缩以在 token 限制内继续
/clear - 清除所有对话历史并重新开始
/status - 检查当前 token 使用情况和限制


fastapi+app