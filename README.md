# QFlowAgent

面向长篇网络小说创作的多阶段/多智能体写作 Agent，基于 LangGraph 构建。核心围绕“上下文系统标准化、流程化节点架构、可审阅的人机协作、可检索的记忆存储”。

## 亮点
- 统一上下文系统：`ContextBuilder` 结构化拼装 SystemMessage，支持分节、约束、示例、Schema、检索片段与 Token 预算裁剪（策略化删除+安全截断）。
- 函数式节点范式：统一改为“函数式节点 + NodeKit 工具层 + 并发 tasks（Send/join）”，已清退独立的人审节点与厚重的基类层级。
- 向量化记忆：基于 `AsyncPostgresStore`（pgvector）将设定/步骤/场景分块入库，支持 `asearch` 语义检索。
- 写作流水线：流式写作缓冲区（连续生成）→ 智能分章（3000~5000字）→ 正文入库与本地保存。
- 模型分层与重试：JSON/TEXT 双模式、LOW/MEDIUM/HIGH 分层、同级多模型权重随机与自动升级重试。

---

## 使用与命令行

请参阅 `README_cmd.md` 获取安装、运行、命令与配置的完整说明（统一入口：`uv run apps/main.py`；可选 `--book-name` 指定书名，不传则由策划阶段确定）。

从最新 checkpoint 恢复（支持配置的特殊值 `latest`，独立于是否指定 `resume_book_id`）：

```bash
uv run apps/main.py --checkpoint-id latest
```

全局约定：`book_id` 等于 `thread_id`。在 `bs_planner` 初始化时将 `configurable.thread_id` 写入 `state.book_id`，用于本地目录与存储命名空间；详见 `README_cmd.md` 的“运行配置”。

## 架构总览

更多节点架构细节，请参阅 [README_nodes.md](README_nodes.md)。

```mermaid
flowchart TD
  B["bs_planner"] --> C["bs_steps"]
  C -->|"条件"| D["bs_refine"]
  D --> E["writer_outline"]
  E --> F["writer_scene"]
  F --> G["stream_write"]
  G -->|"缓冲达阈值"| H["chapter_segment"]
  H --> J["prepare_next_chapter"]
  J -->|"继续写作"| G
  J -->|"补足场景/下一卷"| F
  J -->|"完成"| I["__end__"]
```

- 入口：`src/functional/entrypoint.py` 暴露 `qflow_workflow`（Functional API）；持久化采用 Postgres Checkpointer，返回值与保存值解耦（entrypoint.final）。
- 状态：`src/state.py` 定义 `State(TypedDict)` 与序列化/压缩策略（LangChain dumps/loads + lzma + b64 前缀 `B`/`J`）。
- 模型与请求：`src/common/llm_config.py` 提供 JSON/TEXT 双模式的分层与重试，`src/common/llms.py` 统一初始化模型；请求封装于 `src/common/utils/llm_request_utils.py`。
- 存储：`src/nodes/common/store_manager.py` 以命名空间管理 Writer 团队数据（v2：`writer_app_v2`），计划/设定/大纲/场景/章节统一入库并可分块向量检索。

---

## 上下文系统（Context System）

入口：`src/context/context_builder.py`
- 关键能力：
  - `header()`：稳定头（语言=中文、写作角色说明；不包含动态时间以提升前缀缓存命中率）。
  - `section(title, content)`：结构化分节，例如“设定集”“场景信息”“相关角色设定”。
  - `constraints()`：统一约束（仅允许使用已设定角色等）。
  - `json_schema(schema)`：显式声明输出 Schema（纯 JSON 输出要求）。
  - `demo(text)`：可选示例参考段。
  - `attach_retrieval(chunks)`：附加检索片段。
  - `previous_summary(text)`：历史摘要/上一章要点。
  - `build(max_tokens, policy)`：基于 Token 预算的“策略式裁剪 + 渐进截断”，`policy` 控制优先保留/删除的分节权重（chapter/scene/outline/bs_planner/...）。

最小示例：

```python
from src.context.context_builder import ContextBuilder

cb = ContextBuilder()
messages = (
  cb.header()
    .section("书籍信息", "书名：...")
    .section("设定集", refined_book_setting)
    .json_schema(Chapter.model_json_schema())
    .build(max_tokens=3200, policy="stream")
)
```

各业务节点均通过该构建器生成一致风格的 SystemMessage，避免模板分散与冗余。

---

## 节点与执行框架

入口：函数式节点 + `NodeKit`

- 每个业务节点为 `async def node(state, config) -> Command`，仅描述最小业务逻辑。
- 工具层 `NodeKit` 统一提供：`llm_text/llm_json`、`write_text_under_book()/write_batch_under_book()`、`build_resume_info()/build_stream_resume_info()`、`rel_path_for_chapter()`、`save_plan_to_db()/save_chapter_to_db()`。

典型业务节点：
- 设定规划：`bs_planner` → 生成 `WriterPlan` 并落库/落盘。
- 设定执行/精炼：`bs_steps`/`bs_refine` → 逐步产出/精炼设定，分块入库并向量化。
- 角色：`writer_character`（多阶段：简介集合 → 详情逐个）。
- 大纲：`writer_outline`（多阶段：整书卷纲 → 结合进度的卷细化）。
- 场景：`writer_scene`（多阶段：初始化结构 → 按进度细化卷场景）。
- 写作：`stream_write`（缓冲持续写）→ `chapter_segment`（智能分章）。

---


## 存储与检索（Memory & Store）

- 工具：`src/memory/store_utils.py` 提供 `get_store_and_checkpointer()`、`store_aput/aget/asearch`、分块器。
- 管理器：`src/nodes/common/store_manager.py` 提供 `WriterStoreManager`，按命名空间组织（v2）：
  - `(writer_app_v2, book_id, plan|settings|outline|scene|chapters)`
  - 步骤/场景/设定支持“完整内容 + 分块入库（可向量检索）”。

---

## 模型配置（LLM）

- 策略：`src/common/llm_config.py` 提供 JSON/TEXT 双模式的 LOW/MEDIUM/HIGH 分层、同级多模型权重随机与自动升级重试。具体命令与示例请见 `README_cmd.md`。

---

---

## 许可证

本仓库遵循原始上游依赖的许可证要求（见各依赖项目）。