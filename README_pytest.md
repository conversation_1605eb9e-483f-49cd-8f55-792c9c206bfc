## Pytest 使用指南（QFlowAgent）

本指南面向后来者，讲解如何在本项目中编写与运行单元测试。命令统一使用 `uv run` 前缀。

当前状态：项目已在 `unitest/` 目录提供基础用例（含 join 节点在飞预览/重派发等）。默认仅收集 `unitest/` 目录（见 `pytest.ini:testpaths`）。历史上的自用脚本仍保留在 `tests/`，不会被默认收集。

### 测试发现规则

- **默认规则**：pytest 会递归搜索项目下文件名匹配 `test*.py` 或 `*_test.py` 的文件。
- **函数/方法**：在上述文件内，名称以 `test_` 开头的函数会被收集执行。
- **重要提示**：pytest 会在“收集阶段”导入测试文件，任何顶层可执行代码也会被执行（即使没有 `test_` 函数）。

### 快速开始：写你的第一个单测

1) 在 `unitest/` 下新建文件，文件名以 `test_` 开头，例如：`unitest/test_utils_time_utils.py`

2) 参考示例（已内置一个基础示例：`unitest/test_sample.py`）：

```python
import re

from src.common.utils.time_utils import (
    get_prompt_timestamp,
    get_db_timestamp,
    get_str_timestamp,
)


def test_get_db_timestamp_format():
    ts = get_db_timestamp()
    assert isinstance(ts, str)
    assert re.match(r"^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{6}$", ts)


def test_get_str_timestamp_format():
    ts = get_str_timestamp()
    assert isinstance(ts, str)
    assert re.match(r"^\d{8}_\d{6}_\d{6}$", ts)


async def test_async_example():
    # 示例异步测试（pytest 已在 pytest.ini 中启用 --asyncio-mode=auto）
    assert 1 + 1 == 2
```

3) 运行：

```bash
uv run pytest -q -s
```

说明：
- 放在 `unitest/` 的 `test_*.py` 会被默认收集。
- 若需要编写依赖外部服务（数据库/网络/LLM）的测试，建议加上标记（例如 `@pytest.mark.external`），并在命令行显式选择运行，避免影响默认用例的稳定性。

### 历史自用脚本（默认不收集）

`tests/` 目录保留了历史脚本，仅在显式指定路径时运行。例如：

- `tests/test_writer_store.py`
  - 收集到的测试：`test_writer_store`（async）。
  - 依赖：Postgres（pgvector）、阿里 DashScope Embedding（`ALI_API_KEY`）。
- `tests/test_cot_llm.py`
  - 收集到的测试（均为 async）：
    - `test_deepseek_via_openai_provider`
    - `test_deepseek_via_chatdeepseek`
  - 依赖：DeepSeek/GLM/PPIO 等多家 LLM Provider 的 Base URL 与 API Key。
- `tests/test_glm.py`
  - 不包含 `test_` 函数，但有大量顶层可执行代码；在收集阶段即会调用 GLM 接口并产生网络请求。
- `tests/test_tool_node.py`
  - 不包含 `test_` 函数，但有顶层可执行代码；在收集阶段会执行 `ToolNode(...).invoke(...)`。

综上：默认仅收集 `unitest/`；如显式指定 `tests/` 路径运行，可能触发外部请求或副作用脚本（如 `tests/test_glm.py`）。

### 运行前准备

- **Python 版本**：3.12（见 `pyproject.toml`）。
- **安装依赖**（包含测试插件）：

  ```bash
  uv pip install -e '.[test]'
  ```

- **数据库（pgvector）**：需要本地 Postgres（包含 pgvector 拓展）。可直接启动预置容器：

  ```bash
  bash scripts/init_pg.sh
  ```

  测试默认连接串：`postgresql://postgres:postgres@localhost:5432/qflow?sslmode=disable`（见 `src/memory/store_utils.py`）。

- **环境变量（强制）**：部分模块在导入阶段即断言需要以下变量。可在 Shell 导出，或新建 `src/common/models/.env`（被 `model_list.py` 自动加载）。

  必需键：
  - `DEEPSEEK_BASE_URL`, `DEEPSEEK_API_KEY`
  - `PPIO_BASE_URL`, `PPIO_API_KEY`
  - `ALI_BASE_URL`, `ALI_API_KEY`
  - `SHANGTANG_BASE_URL`, `SHANGTANG_API_KEY`
  - `OPENROUTER_BASE_URL`, `OPENROUTER_API_KEY`
  - `GLM_BASE_URL`, `GLM_API_KEY`
  - `KIMI_BASE_URL`, `KIMI_API_KEY`

  示例（请替换为真实值）：

  ```dotenv
  # src/common/models/.env
  DEEPSEEK_BASE_URL=https://api.deepseek.com
  DEEPSEEK_API_KEY=sk-...
  PPIO_BASE_URL=https://api.ppio.ai
  PPIO_API_KEY=sk-...
  ALI_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1
  ALI_API_KEY=sk-...
  SHANGTANG_BASE_URL=https://api.shangtang.ai
  SHANGTANG_API_KEY=sk-...
  OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
  OPENROUTER_API_KEY=sk-...
  GLM_BASE_URL=https://open.bigmodel.cn/api/paas/v4
  GLM_API_KEY=sk-...
  KIMI_BASE_URL=https://api.moonshot.cn/v1
  KIMI_API_KEY=sk-...
  ```

### 基本运行方式

- **运行全部测试（默认只收集 `unitest/`）**：

  ```bash
  uv run pytest -q -s --asyncio-mode=auto
  ```

- **仅运行某个文件/用例**：

  ```bash
  uv run pytest unitest/test_sample.py -q -s --asyncio-mode=auto
  uv run pytest unitest -k test_get_str_timestamp_format -q -s --asyncio-mode=auto
  ```

- **运行历史 `tests/` 下的脚本（显式指定路径）**：
  ```bash
  uv run pytest tests/test_cot_llm.py -q -s --asyncio-mode=auto
  ```

- **按文件手动忽略**（如果你想覆盖默认行为或新增忽略项）：

  ```bash
  uv run pytest -q -s --asyncio-mode=auto \
    --ignore=tests/test_glm.py \
    --ignore=tests/test_tool_node.py
  ```

- **仅运行 DeepSeek 相关测试（需显式指定路径，且准备 LLM 环境变量）**：

  ```bash
  uv run pytest tests/test_cot_llm.py -q -s --asyncio-mode=auto -k "deepseek"
  ```

- **生成覆盖率报告**：

  ```bash
  uv run pytest --cov=src --cov-report=term-missing -q
  ```

### 异步测试注意事项

- 本仓库存在 `async def` 测试函数，建议通过 `--asyncio-mode=auto` 运行。
- 如果使用旧版 `pytest-asyncio`，可能要求为每个异步用例添加 `@pytest.mark.asyncio` 装饰器，或在 pytest 配置中设置 `asyncio_mode = auto`。本仓库已在 `pytest.ini` 中通过 `addopts` 指定了 `--asyncio-mode=auto`。

### 常见问题排查

- **导入时报错（环境变量缺失）**：`src/common/models/model_list.py` 在导入时断言多个 Provider 的 Base URL 与 API Key 存在。请按上文准备 `.env` 或导出环境变量。
- **导入阶段触发网络请求**：`tests/test_glm.py` 与 `tests/test_tool_node.py` 含顶层可执行代码。若不希望在收集阶段执行，请使用 `--ignore` 排除、显式指定 `unitest/`、或重命名文件。
- **Postgres 连接失败**：确认已执行 `bash scripts/init_pg.sh`，容器运行正常且端口 `5432` 未被占用。
- **DashScope/Kimi/DeepSeek/GLM 调用失败**：检查对应 `*_BASE_URL` 与 `*_API_KEY` 是否正确，或是否命中出网限制/限流。

### 建议的改进（可选）

- 为所有 `async def` 测试显式添加 `@pytest.mark.asyncio`，并为网络/外部依赖测试添加标记（如 `@pytest.mark.external`），便于筛选执行。
- 将 `tests/test_glm.py`、`tests/test_tool_node.py` 的顶层可执行逻辑迁移到 `if __name__ == "__main__":` 或改名为 `examples_*.py`，避免收集阶段副作用。


